import React, { useState, useEffect, useRef } from 'react';
import { useProject } from '../context/ProjectContext.tsx';
import { useReviewer } from '../context/ReviewerContext.tsx';
import { literatureService } from '../services/literatureService.ts';
import { reviewService } from '../services/reviewService.ts';
import { Literature } from '../types/index.ts';
import '../styles/AddFullText.css';
import { processService } from '../services/processService.ts';
import { useFilters } from '../context/FilterContext.tsx';
import { optionsService, QuickReviewReason } from '../services/optionsService.ts';

export function AddFullText() {
    const { currentProject } = useProject();
    const { currentReviewer } = useReviewer();
    const [literatureWithoutFullText, setLiteratureWithoutFullText] = useState<Literature[]>([]);
    const [totalLiteratureWithoutFullText, setTotalLiteratureWithoutFullText] = useState<number>(0);
    const [loading, setLoading] = useState(false);
    const [message, setMessage] = useState('');
    const [selectedReviewerId, setSelectedReviewerId] = useState<number | null>(null);
    const [secondReviewerId, setSecondReviewerId] = useState<number | null>(null);
    const [rejectReasons, setRejectReasons] = useState<{ [key: number]: string }>({});
    const [quickReasons, setQuickReasons] = useState<QuickReviewReason[]>([]);
    const [quickReasonsLoaded, setQuickReasonsLoaded] = useState(false);
    const [retrievingFullText, setRetrievingFullText] = useState<{ [key: number]: boolean }>({});
    const fileInputRefs = useRef<{ [key: number]: HTMLInputElement | null }>({});
    const { lit_filter } = useFilters();

    const itemsPerPage = 100;

    useEffect(() => {
        if (currentProject) {
            fetchLiteratureWithoutFullText();
        }
    }, [currentProject, selectedReviewerId, secondReviewerId]);

    useEffect(() => {
        if (!quickReasonsLoaded) {
            fetchQuickReasons();
            setQuickReasonsLoaded(true);
        }

        const removeListener = optionsService.addQuickReasonsListener(() => {
            fetchQuickReasons();
        });

        return () => {
            removeListener();
        };
    }, [quickReasonsLoaded]);

    const fetchQuickReasons = async () => {
        try {
            const reasons = await optionsService.getQuickReasons();
            setQuickReasons(reasons);
        } catch (error) {
            console.error('Error fetching quick reasons:', error);
        }
    };

    const fetchLiteratureWithoutFullText = async () => {
        if (!currentProject) return;

        setLoading(true);
        try {
            // Create reviewer decision filters
            const reviewerFilters: Array<{
                reviewer_id: number;
                decision: string | null;
                decision_operator: 'equals' | 'not_equals';
            }> = [];

            // If a specific reviewer is selected, filter by that reviewer with ACCEPTED or NEEDS_FULL_TEXT status
            if (selectedReviewerId) {
                reviewerFilters.push({
                    reviewer_id: selectedReviewerId,
                    decision: '',
                    decision_operator: 'not_equals',
                });
            }

            // Add second reviewer filter to exclude papers rejected by this reviewer
            if (secondReviewerId) {
                reviewerFilters.push({
                    reviewer_id: secondReviewerId,
                    decision: 'REJECTED',
                    decision_operator: 'not_equals',
                });
            }

            // We no longer need to use dummy reviewers since we can use review_statuses

            const filter = {
                project_id: currentProject.id,
                paper_status: 'full_text_unavailable',
                reviewer_decision_filters: reviewerFilters,
                page: 1,
                per_page: itemsPerPage,
                // Use the new review_statuses field to filter by both ACCEPTED and NEEDS_FULL_TEXT
                review_statuses: ['accepted', 'needs_full_text'],
                review_status: null, // Keep this for backward compatibility
                search: null,
                category_filters: null,
                reviewer_filters: null,
                review_type: null,
                view_mode: 'all' as 'all',
                journal: null,
            };

            const response = await literatureService.getLiterature({
                page: 1,
                perPage: itemsPerPage,
                lit_filter: filter,
            });

            setTotalLiteratureWithoutFullText(response.total);

            // Filter to only include items with DOIs but no full text
            const filteredItems = response.items.filter(
                (item: Literature) => item.doi && !item.full_text_available
            );

            setLiteratureWithoutFullText(filteredItems);
        } catch (error) {
            console.error('Error fetching literature without full text:', error);
            setMessage('Failed to fetch literature without full text');
        } finally {
            setLoading(false);
        }
    };

    const retrieveFullTexts = async () => {
        if (!currentProject) return;

        try {
            const response = await processService.retrieveFullTexts(lit_filter);
            if (response.ok) {
                setMessage('Full text retrieval started for filtered papers');
                console.log('Full text retrieval started for filtered papers');
            }
        } catch (error) {
            console.error('Error starting full text retrieval:', error);
            setMessage('Error starting full text retrieval');
        }
    };

    const retrieveSingleFullText = async (literatureId: number) => {
        if (!currentProject) return;

        try {
            // Set the loading state for this specific literature item
            setRetrievingFullText((prev) => ({
                ...prev,
                [literatureId]: true,
            }));

            setMessage(`Retrieving full text for paper ID ${literatureId}...`);

            const result = await literatureService.retrieveSingleFullText(literatureId);

            if (result.success) {
                setMessage(result.message);
                // Update the list to remove the item that now has full text
                setLiteratureWithoutFullText((prev) => prev.filter((item) => item.id !== literatureId));
            } else {
                setMessage(`Failed to retrieve full text: ${result.message}`);
            }
        } catch (error) {
            console.error('Error retrieving full text:', error);
            setMessage('Error retrieving full text');
        } finally {
            // Clear the loading state
            setRetrievingFullText((prev) => ({
                ...prev,
                [literatureId]: false,
            }));
        }
    };

    const handleFileUpload = async (literatureId: number, event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file && file.type === 'application/pdf') {
            const formData = new FormData();
            formData.append('pdf', file);

            try {
                setMessage(`Uploading PDF for literature ID ${literatureId}...`);
                const result = await reviewService.uploadPdf(literatureId, formData);
                if (result.item) {
                    setMessage(`PDF uploaded successfully for "${result.item.title}"`);
                    // Update the list
                    setLiteratureWithoutFullText((prev) => prev.filter((item) => item.id !== literatureId));
                }
                // Clear the file input
                if (event.target) {
                    event.target.value = '';
                }
            } catch (error) {
                console.error('Error uploading PDF:', error);
                setMessage('Failed to upload PDF');
            }
        }
    };

    const handleRejectReview = async (literatureId: number, reviewerId: number) => {
        if (!reviewerId) {
            setMessage('Please select a reviewer to reject the paper');
            return;
        }

        try {
            setMessage(`Rejecting paper ID ${literatureId}...`);
            const reason = rejectReasons[literatureId] || 'Full text not available or accessible';

            const result = await reviewService.submitReview({
                literature_id: literatureId,
                reviewer_id: reviewerId,
                decision: 'rejected',
                reason: reason,
            });

            if (result) {
                setMessage(`Paper rejected successfully`);
                // Update the list to remove the rejected paper
                setLiteratureWithoutFullText((prev) => prev.filter((item) => item.id !== literatureId));
            }
        } catch (error) {
            console.error('Error rejecting paper:', error);
            setMessage('Failed to reject paper');
        }
    };

    const handleRejectReasonChange = (literatureId: number, reason: string) => {
        setRejectReasons((prev) => ({
            ...prev,
            [literatureId]: reason,
        }));
    };

    const handleQuickReview = async (
        literatureId: number,
        reviewerId: number,
        reasonText: string,
        isAccept: boolean
    ) => {
        if (!reviewerId) {
            setMessage('Please select a reviewer to reject the paper');
            return;
        }

        try {
            setMessage(`${isAccept ? 'Accepting' : 'Rejecting'} paper ID ${literatureId}...`);

            const result = await reviewService.submitReview({
                literature_id: literatureId,
                reviewer_id: reviewerId,
                decision: isAccept ? 'accepted' : 'rejected',
                reason: reasonText,
            });

            if (result) {
                setMessage(`Paper ${isAccept ? 'accepted' : 'rejected'} successfully`);
                // If rejected, update the list to remove the paper
                if (!isAccept) {
                    setLiteratureWithoutFullText((prev) => prev.filter((item) => item.id !== literatureId));
                }
            }
        } catch (error) {
            console.error(`Error ${isAccept ? 'accepting' : 'rejecting'} paper:`, error);
            setMessage(`Failed to ${isAccept ? 'accept' : 'reject'} paper`);
        }
    };

    return (
        <div className="add-fulltext-container">
            <div className="add-fulltext-explanation">
                <h2>Add Full Text</h2>
                <p>
                    This tool helps you add full text to literature entries that have DOIs but are missing the
                    full text. You can either upload PDFs manually or try to retrieve them automatically from
                    online sources.
                </p>
            </div>

            <div className="add-fulltext-filters">
                <h3>Filter Options</h3>
                <div className="filter-controls">
                    <div className="filter-group">
                        <label htmlFor="reviewer-select">Reviewer:</label>
                        <select
                            id="reviewer-select"
                            value={selectedReviewerId || ''}
                            onChange={(e) =>
                                setSelectedReviewerId(e.target.value ? Number(e.target.value) : null)
                            }>
                            <option value="">All Reviewers</option>
                            {currentReviewer?.map((reviewer) => (
                                <option key={reviewer.id} value={reviewer.id}>
                                    {reviewer.name} ({reviewer.type})
                                </option>
                            ))}
                        </select>
                    </div>

                    <div className="filter-group">
                        <label htmlFor="second-reviewer-select">Exclude Rejected By:</label>
                        <select
                            id="second-reviewer-select"
                            value={secondReviewerId || ''}
                            onChange={(e) =>
                                setSecondReviewerId(e.target.value ? Number(e.target.value) : null)
                            }>
                            <option value="">None</option>
                            {currentReviewer?.map((reviewer) => (
                                <option key={reviewer.id} value={reviewer.id}>
                                    {reviewer.name} ({reviewer.type})
                                </option>
                            ))}
                        </select>
                    </div>
                </div>
            </div>

            <div className="add-fulltext-stats">
                <h3>Statistics</h3>
                <p>
                    Number of literature entries needing full text:
                    <strong>
                        {itemsPerPage}/{totalLiteratureWithoutFullText}
                    </strong>
                </p>
            </div>

            {message && <div className="add-fulltext-message">{message}</div>}

            <div className="add-fulltext-actions">
                <button onClick={retrieveFullTexts} className="retrieve-fulltext-button">
                    Retrieve Full Texts
                </button>
                <button
                    onClick={fetchLiteratureWithoutFullText}
                    disabled={loading}
                    className="secondary-button">
                    {loading ? 'Refreshing...' : 'Refresh List'}
                </button>
            </div>

            <div className="add-fulltext-list">
                <h3>Literature Entries Needing Full Text</h3>
                {literatureWithoutFullText.length === 0 ? (
                    <p>
                        All literature entries have full text or no entries match the current filter criteria.
                    </p>
                ) : (
                    <div className="literature-without-fulltext">
                        {literatureWithoutFullText.map((lit) => (
                            <div key={lit.id} className="literature-item">
                                <div className="literature-details">
                                    <h4>{lit.title}</h4>
                                    <p>
                                        <strong>Authors: <AUTHORS>
                                    </p>
                                    <p>
                                        <strong>Journal:</strong> {lit.journal}
                                    </p>
                                    <p>
                                        <strong>Year:</strong> {lit.year}
                                    </p>
                                    <p className="doi-link">
                                        <strong>DOI:</strong>{' '}
                                        <a
                                            data-paperid={lit.id}
                                            href={reviewService.getDoiUrl(lit.doi)}
                                            target="_blank"
                                            rel="noopener noreferrer">
                                            {lit.doi}
                                        </a>
                                    </p>
                                    <div className="external-search">
                                        <a
                                            href={`https://scholar.google.com/scholar?q="${encodeURIComponent(
                                                lit.title
                                            )}"`}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="google-scholar-link">
                                            Search in Google Scholar
                                        </a>
                                        <a
                                            href={`https://www.google.com/search?q="${encodeURIComponent(
                                                lit.title
                                            )}"`}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="google-link">
                                            Search in Google
                                        </a>
                                    </div>
                                </div>
                                <div className="fulltext-actions">
                                    <div className="pdf-upload">
                                        <label htmlFor={`pdf-upload-${lit.id}`}>Upload PDF:</label>
                                        <input
                                            id={`pdf-upload-${lit.id}`}
                                            type="file"
                                            accept=".pdf"
                                            onChange={(e) => handleFileUpload(lit.id, e)}
                                            ref={(el) => {
                                                fileInputRefs.current[lit.id] = el;
                                                return undefined;
                                            }}
                                            className="pdf-upload-input"
                                        />
                                    </div>
                                    <button
                                        className="retrieve-fulltext-button"
                                        onClick={() => {
                                            window.open(reviewService.getDoiUrl(lit.doi), '_blank');
                                        }}>
                                        Open DOI Link
                                    </button>

                                    <button
                                        className="retrieve-single-fulltext-button"
                                        onClick={() => retrieveSingleFullText(lit.id)}
                                        disabled={retrievingFullText[lit.id]}>
                                        {retrievingFullText[lit.id] ? 'Retrieving...' : 'Retrieve Full Text'}
                                    </button>

                                    {/* Reject paper buttons for manual reviewers */}
                                    <div className="reject-actions">
                                        <h4>Reject Paper:</h4>

                                        {/* Quick review buttons */}
                                        <div className="quick-review-buttons">
                                            {currentReviewer
                                                ?.filter((r) => r.type === 'user')
                                                .map((reviewer) => (
                                                    <div key={reviewer.id} className="reviewer-quick-buttons">
                                                        <h5>{reviewer.name}</h5>
                                                        <div className="quick-reject-buttons">
                                                            {quickReasons
                                                                .filter((reason) => reason.type === 'reject')
                                                                .map((reason) => (
                                                                    <button
                                                                        key={reason.id}
                                                                        className="quick-reject-button"
                                                                        onClick={() =>
                                                                            handleQuickReview(
                                                                                lit.id,
                                                                                reviewer.id,
                                                                                reason.text,
                                                                                false
                                                                            )
                                                                        }>
                                                                        ✗ {reason.text}
                                                                    </button>
                                                                ))}
                                                        </div>
                                                    </div>
                                                ))}
                                        </div>

                                        <div className="reject-controls">
                                            {currentReviewer
                                                ?.filter((r) => r.type === 'user')
                                                .map((reviewer) => (
                                                    <button
                                                        key={reviewer.id}
                                                        className="reject-button"
                                                        onClick={() => handleRejectReview(lit.id, reviewer.id)}>
                                                        Reject as {reviewer.name}
                                                    </button>
                                                ))}
                                        </div>
                                        <div className="reject-reason">
                                            <input
                                                type="text"
                                                placeholder="Rejection reason (optional)"
                                                value={rejectReasons[lit.id] || ''}
                                                onChange={(e) =>
                                                    handleRejectReasonChange(lit.id, e.target.value)
                                                }
                                                className="reject-reason-input"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
}
