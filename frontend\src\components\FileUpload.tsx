import React from 'react';
import { literatureService } from '../services/literatureService.ts';
import { useProject } from '../context/ProjectContext.tsx';

export function FileUpload() {
    const { currentProject } = useProject();
    const [updateResult, setUpdateResult] = React.useState<{ updated?: number; skipped?: number } | null>(null);
    const [createResult, setCreateResult] = React.useState<{ created?: number; skipped?: number } | null>(null);

    const handleFileUpload = async (
        event: React.ChangeEvent<HTMLInputElement>,
        mode: 'create' | 'update' | 'create-only-new' = 'create'
    ) => {
        const file = event.target.files?.[0];
        if (!file) return;

        const formData = new FormData();
        formData.append('file', file);
        if (currentProject) {
            formData.append('project_id', currentProject.id.toString());
        }

        try {
            let response;

            if (mode === 'update') {
                // Use the update endpoint
                response = await literatureService.uploadRisFileOnlyUpdate(formData);
            } else if (mode === 'create-only-new') {
                // Use the create endpoint with only_new=true
                response = await literatureService.uploadRisFile(formData, true);
            } else {
                // Default: create all entries
                response = await literatureService.uploadRisFile(formData, false);
            }

            if (response.ok) {
                const data = await response.json();
                console.log('File processed:', data);

                if (mode === 'update') {
                    setUpdateResult({
                        updated: data.updated,
                        skipped: data.skipped,
                    });
                } else if (mode === 'create' || mode === 'create-only-new') {
                    setCreateResult({
                        created: data.created,
                        skipped: data.skipped,
                    });
                }
            }
        } catch (error) {
            console.error('Error uploading file:', error);
        }
    };

    const handleFileUploadCreate = async (event: React.ChangeEvent<HTMLInputElement>) => {
        handleFileUpload(event, 'create');
    };

    const handleFileUploadCreateOnlyNew = async (event: React.ChangeEvent<HTMLInputElement>) => {
        handleFileUpload(event, 'create-only-new');
    };

    const handleFileUploadUpdate = async (event: React.ChangeEvent<HTMLInputElement>) => {
        handleFileUpload(event, 'update');
    };

    return (
        <div className="upload-section">
            <h2>Upload RIS File</h2>
            <p>This will create new entries for all records in the RIS file.</p>
            <input type="file" accept=".ris" onChange={handleFileUploadCreate} />

            {createResult && !createResult.skipped && (
                <div className="create-results">
                    <p>
                        <strong>Upload Results:</strong>
                    </p>
                    <p>Records created: {createResult.created}</p>
                </div>
            )}

            <h2>Upload RIS File (Only New)</h2>
            <p>This will only create entries for records that don't already exist (based on DOI).</p>
            <input type="file" accept=".ris" onChange={handleFileUploadCreateOnlyNew} />

            {createResult && createResult.skipped && (
                <div className="create-results">
                    <p>
                        <strong>Upload Results:</strong>
                    </p>
                    <p>Records created: {createResult.created}</p>
                    <p>Records skipped (existing DOI): {createResult.skipped}</p>
                </div>
            )}

            <h2>Update Literature from RIS File</h2>
            <p>This will only update existing literature entries based on matching DOIs.</p>
            <input type="file" accept=".ris" onChange={handleFileUploadUpdate} />

            {updateResult && (
                <div className="update-results">
                    <p>
                        <strong>Update Results:</strong>
                    </p>
                    <p>Records updated: {updateResult.updated}</p>
                    <p>Records skipped (no matching DOI): {updateResult.skipped}</p>
                </div>
            )}
        </div>
    );
}
