:root {
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --text-primary: #333333;
    --text-secondary: #666666;
    --border-color: #e0e0e0;
    --accent-color: #007bff;
    --shadow-color: rgba(0, 0, 0, 0.1);

    /* Add missing variables used in FillDOIs.css */
    --background-primary: #ffffff;
    --background-secondary: #f8f9fa;
    --primary: #007bff;
    --primary-dark: #0056b3;
    --success: #28a745;
    --success-dark: #218838;
    --danger: #dc3545;
    --danger-dark: #bd2130;
    --warning: #ffc107;
    --warning-dark: #e0a800;
    --info-light: #d1ecf1;
    --info-dark: #0c5460;
    --disabled: #cccccc;
}

/* Improve dark mode variables for better contrast */
[data-theme='dark'] {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --border-color: #404040;
    --accent-color: #3498db;
    --shadow-color: rgba(0, 0, 0, 0.3);

    /* Add dark mode values for missing variables */
    --background-primary: #1a1a1a;
    --background-secondary: #2d2d2d;
    --primary: #3498db;
    --primary-dark: #2980b9;
    --success: #2ecc71;
    --success-dark: #25a25a;
    --danger: #e74c3c;
    --danger-dark: #c0392b;
    --warning: #f39c12;
    --warning-dark: #d35400;
    --info-light: #2c3e50;
    --info-dark: #ecf0f1;
    --disabled: #555555;
}
body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.app-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.theme-select {
    padding: 8px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    cursor: pointer;
}

section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--bg-secondary);
}

section h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
}

.tabs {
    display: flex;
    gap: 4px;
    margin-bottom: 20px;
    border-bottom: 2px solid #e0e0e0;
}

.tab-button {
    padding: 12px 24px;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.2s ease;
    color: var(--text-secondary);
    background-color: var(--bg-secondary);
}

.tab-button:hover {
    color: #007bff;
}

.tab-button.active {
    color: var(--accent-color);
    border-bottom: 2px solid var(--accent-color);
    margin-bottom: -2px;
}

/* Styling for disabled tabs */
.tab-button:disabled {
    color: #999;
    border-bottom: none;
    cursor: not-allowed;
    opacity: 0.7;
}

.tab-content {
    padding: 24px;
    background-color: var(--bg-secondary);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.view-mode-controls {
    margin-top: 20px;
}

.view-mode-button {
    padding: 8px 16px;
    margin-right: 10px;
    border: 1px solid #007bff;
    border-radius: 4px;
    background: white;
    color: #007bff;
    cursor: pointer;
}

.view-mode-button.active {
    background: #007bff;
    color: white;
}

.research-topic-input,
.categories-input {
    width: 100%;
    min-height: 100px;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 15px;
    font-family: inherit;
}

.process-controls {
    display: flex;
    gap: 10px;
}

.provider-select {
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.process-button {
    background: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

.process-button:hover {
    background: #2980b9;
}

.literature-list {
    display: grid;
    gap: 20px;
}

.literature-item {
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #95a5a6;
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    box-shadow: 0 2px 4px var(--shadow-color);
}

.literature-item.accepted {
    border-left-color: #2ecc71;
}

.literature-item.rejected {
    border-left-color: #e74c3c;
}

.literature-item.needs_full_text {
    border-left-color: #f1c40f;
}

.literature-item h3 {
    color: #2c3e50;
    margin-top: 0;
}

.authors {
    color: #7f8c8d;
    font-style: italic;
}

.abstract {
    margin: 15px 0;
    line-height: 1.6;
}

.status-section {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9em;
    font-weight: bold;
    text-transform: uppercase;
}

.status-badge.accepted {
    background: #2ecc71;
    color: white;
}

.status-badge.rejected {
    background: #e74c3c;
    color: white;
}

.status-badge.needs_full_text {
    background: #f1c40f;
    color: black;
}

.review-reason {
    margin-top: 10px;
    color: #666;
}

.upload-fulltext {
    margin-top: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
}

.review-decisions {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.review-decisions h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
}

.review-decision {
    background: white;
    padding: 15px;
    margin-bottom: 12px;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    border-left: 4px solid #95a5a6;
}

.review-decision.accepted {
    border-left-color: #2ecc71;
}

.review-decision.rejected {
    border-left-color: #e74c3c;
}

.review-decision.needs_full_text {
    border-left-color: #f1c40f;
}

.review-decision p {
    margin: 8px 0;
    line-height: 1.4;
}

.review-decision strong {
    color: #34495e;
}

.review-decision .timestamp {
    color: #7f8c8d;
    font-size: 0.9em;
}

.review-controls {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    display: grid;
    gap: 12px;
}

.review-controls select,
.review-controls textarea {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 100%;
}

.review-controls button {
    background: #3498db;
    color: white;
    border: none;
    padding: 10px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.2s;
}

.review-controls button:hover {
    background: #2980b9;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin: 2rem 0;
}

.pagination button {
    padding: 0.5rem 1rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    background: #fff;
    cursor: pointer;
}

.pagination button:disabled {
    background: #f0f0f0;
    cursor: not-allowed;
}

.pagination span {
    font-size: 0.9rem;
    color: #666;
}

.filter-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    border-radius: 8px;
    align-items: center;
    background: var(--bg-secondary);
}

.search-input {
    flex: 1;
    padding: 8px;
    border-radius: 4px;
    font-family: inherit;
    background: var(--bg-primary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.filter-select {
    min-width: 180px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    font-size: 14px;
    color: #2c3e50;
    cursor: pointer;
    transition: border-color 0.2s;
}

.filter-select:hover {
    border-color: #3498db;
}

.filter-select:disabled {
    background-color: #f5f6f7;
    cursor: not-allowed;
    opacity: 0.7;
}

.filter-select option {
    padding: 8px;
    font-size: 14px;
}

.doi-link {
    margin: 8px 0;
    font-size: 0.9em;
}

.doi-link a {
    color: #3498db;
    text-decoration: none;
}

.doi-link a:hover {
    text-decoration: underline;
}

.categories {
    margin: 10px 0;
}

.category-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.category-tag {
    background-color: #f0f0f0;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9em;
}

/* Add these styles */
.tab-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.tab-button {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    background: #f0f0f0;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.2s;
}

.tab-button.active {
    background: #3498db;
    color: white;
}

.merge-tools-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.merge-categories,
.merge-values {
    margin-top: 20px;
    display: grid;
    gap: 15px;
}

.merge-tools-section select {
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.merge-tools-section button {
    background: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

.merge-tools-section button:hover {
    background: #2980b9;
}

.category-filter-group {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.remove-filter-button {
    margin-left: 8px;
    padding: 0 8px;
    font-size: 18px;
    cursor: pointer;
    background: #ff4444;
    color: white;
    border: none;
    border-radius: 4px;
}

.remove-filter-button:hover {
    background: #ff0000;
}

.filter-select[multiple] {
    min-height: 100px;
    padding: 8px;
}

.category-filter-group .filter-select {
    min-width: 200px;
}

.filter-history-panel {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.save-filters {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.filter-name-input {
    flex: 1;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f5f5f5;
    border-radius: 4px;
}

.history-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.history-actions {
    display: flex;
    gap: 8px;
}

.retrieve-fulltext-button {
    background: #2ecc71;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

.retrieve-fulltext-button:hover {
    background: #27ae60;
}

.document-links {
    display: flex;
    gap: 20px;
    margin: 10px 0;
}

.pdf-link a {
    color: #27ae60;
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
}

.pdf-link a:hover {
    text-decoration: underline;
}

.reviewer-select {
    min-height: 100px;
    min-width: 200px;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.reviewer-select option {
    padding: 4px;
}

.project-section {
    margin-bottom: 20px;
}

.project-selector {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.project-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.project-form input,
.project-form textarea {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.project-form textarea {
    min-height: 100px;
}

.project-form button {
    padding: 8px 16px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.project-form button:hover {
    background-color: #0056b3;
}

.reload-button {
    padding: 8px 16px;
    margin-left: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: #f8f8f8;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.reload-button:hover {
    background-color: #e8e8e8;
}

.manual-review-form {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    border-top: 1px solid #ccc;
    margin-top: 1rem;
}

.manual-review-form select,
.manual-review-form input {
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.manual-review-form input {
    flex: 1;
}

.filter-controls {
    background: var(--bg-secondary);
}

.search-input {
    background: var(--bg-primary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.filter-select {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.category-tag {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.merge-tools-section {
    background: var(--bg-secondary);
    box-shadow: 0 2px 4px var(--shadow-color);
}

.history-item {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.review-controls select,
.review-controls textarea,
.project-form input,
.project-form textarea {
    background: var(--bg-primary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.review-decision {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.filter-history-panel {
    border-color: var(--border-color);
    background: var(--bg-secondary);
}

.reload-button {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.manual-review-form select,
.manual-review-form input {
    background: var(--bg-primary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.reviewer-select {
    background: var(--bg-primary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.doi-link a {
    color: var(--accent-color);
}

.authors {
    color: var(--text-secondary);
}

.pagination button {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.pagination span {
    color: var(--text-secondary);
}

.filter-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-bottom: 1rem;
}

.per-page-select {
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid #ccc;
    background-color: white;
    cursor: pointer;
}

.per-page-select:hover {
    border-color: #666;
}

.auth-page {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: var(--bg-color);
}

.auth-container {
    width: 400px;
    padding: 2rem;
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.auth-toggle {
    display: flex;
    margin-bottom: 1.5rem;
}

.auth-toggle button {
    flex: 1;
    padding: 0.75rem;
    background: none;
    border: none;
    border-bottom: 2px solid var(--border-color);
    cursor: pointer;
    font-weight: 500;
    color: var(--text-color);
}

.auth-toggle button.active {
    border-bottom-color: var(--primary-color);
    color: var(--primary-color);
}

.login-container,
.register-container {
    padding: 1rem 0;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--input-bg);
    color: var(--text-color);
}

.error-message {
    background-color: rgba(255, 0, 0, 0.1);
    color: #e74c3c;
    padding: 0.75rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}

button[type='submit'] {
    width: 100%;
    padding: 0.75rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    margin-top: 1rem;
}

button[type='submit']:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.user-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    margin-right: auto;
}

.logout-button {
    padding: 0.5rem 1rem;
    background-color: transparent;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-color);
}

/* Add to your CSS file */
.edit-form {
    margin: 20px 0;
    padding: 15px;
    background-color: #f5f5f5;
    border-radius: 5px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.edit-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.edit-actions button {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.edit-actions button[type='submit'] {
    background-color: #4caf50;
    color: white;
}

.edit-actions button[type='button'] {
    background-color: #f44336;
    color: white;
}

.title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.edit-button {
    padding: 5px 10px;
    background-color: #2196f3;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.pdf-status {
    margin-top: 5px;
}

.pdf-available {
    color: green;
    font-weight: bold;
}

.pdf-unavailable {
    color: #e74c3c;
}

.edit-button {
    padding: 5px 10px;
    background-color: #2196f3;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.title-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.export-buttons {
    display: flex;
    gap: 10px;
    margin-left: 10px;
}

.export-button {
    padding: 5px 10px;
    background-color: #4a90e2;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.export-button:hover {
    background-color: #357ab8;
}

.export-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

/* Options Page Styles */
.options-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.options-section {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.options-section h3 {
    margin-top: 0;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--input-bg);
    color: var(--text-color);
}

.message {
    margin-top: 10px;
    padding: 8px;
    border-radius: 4px;
    background-color: #d4edda;
    color: #155724;
}

.quick-reasons-form {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.quick-reasons-form input,
.quick-reasons-form select {
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--input-bg);
    color: var(--text-color);
}

.quick-reasons-form input {
    flex-grow: 1;
}

.quick-reasons-list ul {
    list-style: none;
    padding: 0;
}

.quick-reasons-list li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    margin-bottom: 5px;
    background-color: var(--input-bg);
    border-radius: 4px;
}

.quick-reasons-list button {
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
}

/* Quick Review Button Styles */
.quick-review-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin: 10px 0;
}

.quick-accept-buttons,
.quick-reject-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.quick-accept-button,
.quick-reject-button {
    padding: 5px 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
}

.quick-accept-button {
    background-color: #28a745;
    color: white;
}

.quick-reject-button {
    background-color: #dc3545;
    color: white;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.export-csv-modal {
    background-color: white;
    border-radius: 5px;
    padding: 20px;
    width: 500px;
    max-width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.modal-header h3 {
    margin: 0;
}

.close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
}

.column-section {
    margin-bottom: 20px;
}

.column-section h5 {
    margin-top: 0;
    margin-bottom: 10px;
}

.column-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
}

.column-option label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.column-option input {
    margin-right: 8px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    border-top: 1px solid #eee;
    padding-top: 15px;
}

.modal-footer button {
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

.cancel-button {
    background-color: #f1f1f1;
    border: 1px solid #ddd;
}

.export-button {
    background-color: #4caf50;
    color: white;
    border: none;
}

.export-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.reviewer-category-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.reviewer-category-group {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 10px;
    background-color: #f9f9f9;
}

.reviewer-category-group h6 {
    margin: 0 0 10px 0;
    font-weight: bold;
    color: #333;
}

.category-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}
.review-import {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
}

.import-section {
    margin-bottom: 30px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    padding: 15px;
    background-color: #f9f9f9;
}

.reviewer-select,
.file-input {
    width: 100%;
    padding: 8px;
    margin-top: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.help-text {
    font-size: 0.9em;
    color: #666;
    margin-top: 5px;
}

.column-mapping {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.mapping-row {
    display: flex;
    align-items: center;
}

.mapping-row label {
    width: 120px;
    font-weight: bold;
}

.mapping-row select {
    flex: 1;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.preview-table-container {
    overflow-x: auto;
    max-height: 300px;
    border: 1px solid #ddd;
}

.preview-table {
    width: 100%;
    border-collapse: collapse;
}

.preview-table th,
.preview-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.preview-table th {
    background-color: #f2f2f2;
    position: sticky;
    top: 0;
}

.import-actions {
    margin-top: 20px;
    text-align: center;
}

.import-button {
    padding: 10px 20px;
    background-color: #4caf50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

.import-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.import-results {
    text-align: center;
    padding: 20px;
    background-color: #f0f8ff;
    border-radius: 5px;
}

.results-summary {
    margin: 20px 0;
    font-size: 16px;
}

.error-text {
    color: #d32f2f;
}

.reset-button {
    padding: 10px 20px;
    background-color: #2196f3;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

.identifier-options {
    display: flex;
    gap: 20px;
    margin-top: 10px;
}

.identifier-option {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.identifier-option input {
    margin-right: 8px;
}

.warning-text {
    color: #ff9800;
    font-weight: bold;
    margin-top: 10px;
}

.dry-run-button {
    padding: 10px 20px;
    background-color: #2196f3;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    margin-right: 10px;
}

.dry-run-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.dry-run-results {
    margin-top: 20px;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    background-color: #f5f5f5;
}

.found-items,
.not-found-items {
    margin-top: 20px;
}

.not-found-row {
    background-color: #ffebee;
}

.decision-accepted {
    color: #4caf50;
    font-weight: bold;
}

.decision-rejected {
    color: #f44336;
    font-weight: bold;
}

.decision-needs_full_text {
    color: #ff9800;
    font-weight: bold;
}

.dry-run-actions {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.proceed-button {
    padding: 10px 20px;
    background-color: #4caf50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

.proceed-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.fill-dois-container {
    padding: 20px;
}

.fill-dois-explanation {
    background-color: var(--background-secondary);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.fill-dois-stats {
    margin-bottom: 20px;
}

.fill-dois-message {
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
    background-color: var(--info-light);
    color: var(--info-dark);
}

.fill-dois-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.primary-button,
.secondary-button {
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.primary-button {
    background-color: var(--primary);
    color: white;
    border: none;
}

.primary-button:disabled {
    background-color: var(--disabled);
    cursor: not-allowed;
}

.secondary-button {
    background-color: var(--background-secondary);
    border: 1px solid var(--border-color);
}

.literature-without-dois {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.literature-item {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    background-color: var(--background-primary);
}

.literature-details h4 {
    margin-top: 0;
    margin-bottom: 10px;
}

.potential-matches {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px dashed var(--border-color);
}

.match-item {
    background-color: var(--background-secondary);
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.match-details p {
    margin: 5px 0;
}

.match-actions,
.literature-actions {
    margin-top: 10px;
}

.accept-button {
    background-color: var(--success);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
}

.fake-doi-button {
    background-color: var(--warning);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

.no-matches {
    font-style: italic;
    color: var(--text-secondary);
    margin: 10px 0;
}

.reset-filters-button {
    background-color: #f44336;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 10px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.reset-filters-button:hover {
    background-color: #d32f2f;
}

.reset-filters-button:active {
    background-color: #b71c1c;
}

.category-filter-group {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.filter-mode-toggle {
    margin: 0 8px;
    display: flex;
    align-items: center;
}

.filter-mode-toggle label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.filter-mode-toggle input {
    margin-right: 4px;
}

/* File Upload Styles */
.upload-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--bg-secondary);
}

.upload-section h2 {
    margin-top: 20px;
    margin-bottom: 10px;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 5px;
}

.upload-section p {
    margin-bottom: 10px;
    color: var(--text-secondary);
}

.upload-section input[type="file"] {
    display: block;
    margin-bottom: 20px;
    padding: 10px;
    border: 1px dashed var(--border-color);
    border-radius: 4px;
    background-color: var(--bg-primary);
    width: 100%;
}

.create-results, .update-results {
    margin-top: 15px;
    padding: 10px;
    border-radius: 4px;
    background-color: var(--bg-primary);
    border-left: 4px solid var(--accent-color);
}

.create-results p, .update-results p {
    margin: 5px 0;
}
