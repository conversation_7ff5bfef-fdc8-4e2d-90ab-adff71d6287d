import React, { useEffect, useRef, useState } from 'react';
import { Literature } from '../types';
import { reviewService } from '../services/reviewService.ts';
import { useReviewer } from '../context/ReviewerContext.tsx';
import { literatureService, LiteratureUpdateData } from '../services/literatureService.ts';
import { optionsService } from '../services/optionsService.ts';

interface QuickReviewReason {
    id: number;
    text: string;
    type: 'accept' | 'reject';
}

interface LiteratureItemProps {
    item: Literature;
    review?: boolean;
    selectedReviewer: number;
    onReviewerSelect: (reviewerId: number) => void;
    onReviewNext?: () => void;
    onItemUpdate?: (updatedItem: Literature) => void; // Add this prop
}

export function LiteratureItem({
    item,
    review,
    selectedReviewer,
    onReviewerSelect,
    onReviewNext,
    onItemUpdate,
}: LiteratureItemProps) {
    const [decision, setDecision] = useState('');
    const [reason, setReason] = useState('');
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const { currentReviewer } = useReviewer();

    const reasonInputRef = useRef<HTMLInputElement>(null);

    const hasFullPaperCategories = item.categories.some((cat) => !cat.from_abstract);
    const [showAbstractCategories, setShowAbstractCategories] = useState(!hasFullPaperCategories);
    const [showReviewDecisions, setShowReviewDecisions] = useState(!review);

    // Add local item state to update when changes occur
    const [currentItem, setCurrentItem] = useState<Literature>(item);

    // Update local state when props change
    useEffect(() => {
        setCurrentItem(item);
    }, [item]);

    // Edit state
    const [isEditing, setIsEditing] = useState(false);
    const [editFormData, setEditFormData] = useState<LiteratureUpdateData>({
        title: currentItem.title,
        authors: currentItem.authors,
        abstract: currentItem.abstract,
        journal: currentItem.journal,
        publisher: currentItem.publisher,
        year: currentItem.year,
        doi: currentItem.doi,
        full_text_available: currentItem.full_text_available,
    });

    // Add this new state
    const [quickReasons, setQuickReasons] = useState<QuickReviewReason[]>([]);

    // Add this useEffect to fetch quick reasons
    useEffect(() => {
        if (review) {
            fetchQuickReasons();
        }
    }, [review]);

    useEffect(() => {
        if (review && reasonInputRef.current) {
            reasonInputRef.current.focus();
            setDecision('accepted');
        }
    }, [review]);

    const resetInputFields = () => {
        setDecision('accepted');
        setReason('');
    };

    const submitReview = async () => {
        console.log(currentItem.id, selectedReviewer, decision, reason);
        await reviewService.submitReview({
            literature_id: currentItem.id,
            reviewer_id: selectedReviewer!,
            decision,
            reason,
        });
    };

    const handleReasonKeyDown = async (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            await submitReview();
            resetInputFields();
            if (onReviewNext) {
                onReviewNext();
            }
        } else if (e.currentTarget.value.trim() !== '') {
            setDecision('rejected');
        } else if (e.currentTarget.value.trim() === '') {
            setDecision('accepted');
        }
    };

    const [quickReasonsLoaded, setQuickReasonsLoaded] = useState(false);

    useEffect(() => {
        if (review && !quickReasonsLoaded) {
            fetchQuickReasons();
            setQuickReasonsLoaded(true);
        }

        const removeListener = optionsService.addQuickReasonsListener(() => {
            fetchQuickReasons();
        });

        return () => {
            removeListener();
        };
    }, [review, quickReasonsLoaded]);

    const fetchQuickReasons = async () => {
        try {
            const reasons = await optionsService.getQuickReasons();
            setQuickReasons(reasons);
        } catch (error) {
            console.error('Error fetching quick reasons:', error);
        }
    };
    // Add this function to handle quick review submission
    const handleQuickReview = async (reasonText: string, isAccept: boolean) => {
        if (!selectedReviewer) {
            alert('Please select a reviewer first');
            return;
        }
        await reviewService.submitReview({
            literature_id: currentItem.id,
            reviewer_id: selectedReviewer!,
            decision: isAccept ? 'accepted' : 'rejected',
            reason: reasonText,
        });
        resetInputFields();
        if (onReviewNext) onReviewNext();
    };

    // Handle edit form submission
    const handleEditSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        try {
            const result = await literatureService.updateLiterature(currentItem.id, editFormData);
            if (result.item) {
                setCurrentItem(result.item);
                if (onItemUpdate) {
                    onItemUpdate(result.item);
                }
            }
            setIsEditing(false);
        } catch (error) {
            console.error('Error updating literature:', error);
        }
    };

    // Update the file upload handler
    const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file && file.type === 'application/pdf') {
            setSelectedFile(file);
            const formData = new FormData();
            formData.append('pdf', file);

            try {
                const result = await reviewService.uploadPdf(currentItem.id, formData);
                if (result.item) {
                    setCurrentItem(result.item);
                    if (onItemUpdate) {
                        onItemUpdate(result.item);
                    }
                }
                // Clear the file input
                event.target.value = '';
                setSelectedFile(null);
            } catch (error) {
                console.error('Error uploading PDF:', error);
            }
        }
    };

    // Update all references to item properties to use currentItem instead
    const doiUrl = currentItem.doi ? reviewService.getDoiUrl(currentItem.doi) : null;
    const pdfUrl =
        currentItem.doi && currentItem.full_text_available
            ? literatureService.getPdfUrl(currentItem.doi)
            : null;

    // Handle edit form input changes
    const handleEditChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setEditFormData((prev) => ({
            ...prev,
            [name]: name === 'year' ? (value ? parseInt(value) : null) : value,
        }));
    };

    // Render method - update to use currentItem instead of item
    return (
        <div className="literature-item">
            {!isEditing ? (
                <>
                    <div className="title-row">
                        <h3>{currentItem.title}</h3>
                        <button
                            className="edit-button"
                            onClick={() => {
                                setEditFormData({
                                    title: currentItem.title,
                                    authors: currentItem.authors,
                                    abstract: currentItem.abstract,
                                    journal: currentItem.journal,
                                    publisher: currentItem.publisher,
                                    year: currentItem.year,
                                    doi: currentItem.doi,
                                    full_text_available: currentItem.full_text_available,
                                });
                                setIsEditing(true);
                            }}>
                            Edit
                        </button>
                    </div>
                    <p className="authors">Authors: <AUTHORS>
                    <div className="document-links">
                        {currentItem.url ? (
                            <p className="paper-link">
                                Paper URL:{' '}
                                <a href={currentItem.url} target="_blank" rel="noopener noreferrer">
                                    {currentItem.url}
                                </a>
                            </p>
                        ) : doiUrl ? (
                            <p className="doi-link">
                                DOI:{' '}
                                <a
                                    href={doiUrl}
                                    data-paperid={currentItem.id}
                                    target="_blank"
                                    rel="noopener noreferrer">
                                    {currentItem.doi}
                                </a>
                            </p>
                        ) : null}
                        <p className="journal">Journal: {currentItem.journal}</p>
                        {currentItem.publisher && (
                            <p className="publisher">Publisher: {currentItem.publisher}</p>
                        )}

                        {/* Show PDF status */}
                        <p className="pdf-status">
                            PDF Status:{' '}
                            {currentItem.full_text_available ? (
                                <span className="pdf-available">Available</span>
                            ) : (
                                <span className="pdf-unavailable">Not Available</span>
                            )}
                        </p>

                        {pdfUrl && (
                            <p className="pdf-link">
                                <a href={pdfUrl} target="_blank" rel="noopener noreferrer">
                                    View Full Text PDF
                                </a>
                            </p>
                        )}
                    </div>
                    {!currentItem.full_text_available && (
                        <div className="pdf-upload">
                            <h4>Manual PDF Upload</h4>
                            <input
                                type="file"
                                accept=".pdf"
                                onChange={handleFileUpload}
                                className="pdf-upload-input"
                            />
                            {selectedFile && <p className="selected-file">Selected: {selectedFile.name}</p>}
                        </div>
                    )}
                    <div className="categories">
                        {currentItem.categories.some((cat) => cat.from_abstract) && (
                            <div className="abstract-categories">
                                <h4>
                                    Categories Abstract{' '}
                                    {hasFullPaperCategories && (
                                        <button
                                            onClick={() => setShowAbstractCategories(!showAbstractCategories)}>
                                            {showAbstractCategories ? 'Hide' : 'Show'}
                                        </button>
                                    )}
                                </h4>
                                {showAbstractCategories && (
                                    <div className="category-tags">
                                        {currentItem.categories
                                            .filter((cat) => cat.from_abstract)
                                            .map((cat, index) => (
                                                <span key={index} className="category-tag">
                                                    <strong>{cat.category}:</strong> {cat.value}
                                                </span>
                                            ))}
                                    </div>
                                )}
                            </div>
                        )}
                        {currentItem.categories.some((cat) => !cat.from_abstract) && (
                            <div className="fullpaper-categories">
                                <h4>Categories Full-Paper</h4>
                                <div className="category-tags">
                                    {currentItem.categories
                                        .filter((cat) => !cat.from_abstract)
                                        .map((cat, index) => (
                                            <span key={index} className="category-tag">
                                                <strong>{cat.category}:</strong> {cat.value}
                                            </span>
                                        ))}
                                </div>
                            </div>
                        )}
                    </div>
                    <h3>{currentItem.title}</h3>
                    <div className="abstract">
                        <strong>Abstract:</strong>
                        <p>{currentItem.abstract}</p>
                    </div>
                    {currentItem.review_decisions.length > 0 && (
                        <div className="review-decisions-section">
                            <h4>
                                Review Decisions{' '}
                                {review && (
                                    <button onClick={() => setShowReviewDecisions(!showReviewDecisions)}>
                                        {showReviewDecisions ? 'Hide' : 'Show'}
                                    </button>
                                )}
                            </h4>
                            {showReviewDecisions &&
                                currentItem.review_decisions.map((reviewDecision, index) => (
                                    <div key={index} className={`review-decision ${reviewDecision.decision}`}>
                                        <p>
                                            <strong>{reviewDecision.reviewer_name}</strong> -{' '}
                                            {new Date(reviewDecision.created_at).toLocaleString()}
                                        </p>
                                        <p>Decision: {reviewDecision.decision.toUpperCase()}</p>
                                        <p>Reason: {reviewDecision.reason}</p>
                                    </div>
                                ))}
                        </div>
                    )}
                    {review && (
                        <div className="review-controls">
                            <select
                                value={selectedReviewer}
                                onChange={(e) => onReviewerSelect(Number(e.target.value))}>
                                <option value="">Select Reviewer</option>
                                {currentReviewer
                                    ?.filter((r) => r.type === 'user')
                                    .map((r) => (
                                        <option key={r.id} value={r.id}>
                                            {r.name} ({r.type})
                                        </option>
                                    ))}
                            </select>
                            <div className="quick-review-buttons">
                                <div className="quick-accept-buttons">
                                    {quickReasons
                                        .filter((reason) => reason.type === 'accept')
                                        .map((reason) => (
                                            <button
                                                key={reason.id}
                                                className="quick-accept-button"
                                                onClick={() => handleQuickReview(reason.text, true)}>
                                                ✓ {reason.text}
                                            </button>
                                        ))}
                                </div>
                                <div className="quick-reject-buttons">
                                    {quickReasons
                                        .filter((reason) => reason.type === 'reject')
                                        .map((reason) => (
                                            <button
                                                key={reason.id}
                                                className="quick-reject-button"
                                                onClick={() => handleQuickReview(reason.text, false)}>
                                                ✗ {reason.text}
                                            </button>
                                        ))}
                                </div>
                            </div>

                            <select value={decision} onChange={(e) => setDecision(e.target.value)}>
                                <option value="accepted">Accept</option>
                                <option value="rejected">Reject</option>
                            </select>
                            <input
                                type="text"
                                ref={reasonInputRef}
                                value={reason}
                                onChange={(e) => {
                                    const value = e.target.value;
                                    setReason(value);
                                }}
                                onKeyDown={handleReasonKeyDown}
                                placeholder="Enter Reject reason or press Enter to submit"
                            />
                            <button
                                onClick={async () => {
                                    await submitReview();
                                    resetInputFields();
                                    if (onReviewNext) onReviewNext();
                                }}>
                                Submit Review
                            </button>
                        </div>
                    )}
                </>
            ) : (
                <form onSubmit={handleEditSubmit} className="edit-form">
                    <div className="form-group">
                        <label htmlFor="title">Title:</label>
                        <input
                            id="title"
                            name="title"
                            value={editFormData.title}
                            onChange={(e) => setEditFormData({ ...editFormData, title: e.target.value })}
                            required
                        />
                    </div>
                    <div className="form-group">
                        <label htmlFor="authors">Authors: <AUTHORS>
                        <input
                            id="authors"
                            name="authors"
                            value={editFormData.authors}
                            onChange={handleEditChange}
                        />
                    </div>
                    <div className="form-group">
                        <label htmlFor="journal">Journal:</label>
                        <input
                            id="journal"
                            name="journal"
                            value={editFormData.journal}
                            onChange={handleEditChange}
                        />
                    </div>
                    <div className="form-group">
                        <label htmlFor="publisher">Publisher:</label>
                        <input
                            id="publisher"
                            name="publisher"
                            value={editFormData.publisher || ''}
                            onChange={handleEditChange}
                        />
                    </div>
                    <div className="form-group">
                        <label htmlFor="year">Year:</label>
                        <input
                            id="year"
                            name="year"
                            type="number"
                            value={editFormData.year || ''}
                            onChange={handleEditChange}
                        />
                    </div>
                    <div className="form-group">
                        <label htmlFor="doi">DOI:</label>
                        <input id="doi" name="doi" value={editFormData.doi} onChange={handleEditChange} />
                    </div>
                    <div className="form-group">
                        <label htmlFor="full_text_available">Full Text Available:</label>
                        <input
                            id="full_text_available"
                            name="full_text_available"
                            type="checkbox"
                            checked={editFormData.full_text_available || false}
                            onChange={(e) =>
                                setEditFormData({
                                    ...editFormData,
                                    full_text_available: e.target.checked,
                                })
                            }
                        />
                    </div>
                    <div className="form-group">
                        <label htmlFor="abstract">Abstract:</label>
                        <textarea
                            id="abstract"
                            name="abstract"
                            value={editFormData.abstract}
                            onChange={handleEditChange}
                            rows={5}
                        />
                    </div>
                    <div className="edit-actions">
                        <button type="submit">Save</button>
                        <button type="button" onClick={() => setIsEditing(false)}>
                            Cancel
                        </button>
                    </div>
                </form>
            )}
        </div>
    );
}
