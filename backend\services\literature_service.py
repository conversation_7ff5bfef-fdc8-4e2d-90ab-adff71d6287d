import asyncio
import json
import os
import re
import concurrent.futures
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List
from urllib.parse import quote

import aiohttp
from config import Settings
from exceptions import LiteratureNotFoundError
from modules.db import Literature, LiteratureCategory, ReviewDecision, Reviewer
from modules.llm import LLMProviderType, get_llm_provider, get_prompt
from pydantic import BaseModel
from services.PdfHandler import PDFHandler
from sqlalchemy import func, or_, and_, select
from sqlalchemy.orm import Session, aliased, joinedload


class ReviewType(Enum):
    ALL = ""
    FULL_PAPER = "full_paper"
    ABSTRACT = "abstract"


class Category(BaseModel):
    category: str | None = ""
    values: List[str] = []
    exclude: bool = False


class ReviewerDecisionFilter(BaseModel):
    reviewer_id: int
    decision: str | None = None  # Can be "ACCEPTED", "REJECTED", "NEEDS_FULL_TEXT"
    decision_operator: str = "equals"  # "equals", "not_equals"


class LiteratureFilter(BaseModel):
    project_id: int = None
    page: int | None = None
    per_page: int | None = None
    review_status: str | None = None
    review_statuses: List[str] | None = None  # New field for multiple review statuses
    paper_status: str | None = None
    search: str | None = None
    category_filters: List[Category] | None = None
    reviewer_filters: str | None = None  # Keep for backward compatibility
    reviewer_decision_filters: List[ReviewerDecisionFilter] | None = None  # New field
    review_type: ReviewType | None = None
    view_mode: str | None = None
    journal: str | None = None
    publisher: str | None = None


class LiteratureService:
    def __init__(self, db: Session):
        self.db = db
        self.pdf_handler = PDFHandler()

    def get_literature(
        self,
        # project_id: int,
        page: int,
        per_page: int,
        lit_filter: LiteratureFilter,
    ) -> Dict[str, Any]:
        query = self.db.query(Literature).distinct()
        query = self._apply_filters(query, lit_filter)

        total = query.distinct().count()

        query = (
            query.options(joinedload(Literature.categories))
            .options(joinedload(Literature.review_decisions).joinedload(ReviewDecision.reviewer))
            .offset((page - 1) * per_page)
            .limit(per_page)
        )

        literature = query.all()
        return self._format_literature_response(literature, total, page, per_page)

    def get_full_texts(
        self,
        lit_filter: LiteratureFilter,
    ) -> Dict[str, Any]:
        query = self.db.query(Literature).distinct()
        query = self._apply_filters(query, lit_filter)

        literature: List[Literature] = query.all()
        full_texts = ""
        for lit in literature:
            full_texts += "###############\n"
            full_texts += f"PAPER ID {lit.id}\n"
            full_texts += f"TITLE {lit.title}\n"
            full_texts += f"FULL TEXT:\n{self.get_full_text(lit.doi)}"
        return full_texts

    def get_disagreeing_reviews(self, project_id, page: int, per_page: int) -> Dict[str, Any]:
        subquery = (
            self.db.query(ReviewDecision.literature_id)
            .group_by(ReviewDecision.literature_id)
            .having(func.count(func.distinct(ReviewDecision.decision)) >= 2)
            .having(func.count() >= 2)
            .subquery()
        )

        query = (
            self.db.query(Literature)
            .join(subquery, Literature.id == subquery.c.literature_id)
            .filter(Literature.project_id == project_id)
            .options(joinedload(Literature.categories))
            .options(joinedload(Literature.review_decisions).joinedload(ReviewDecision.reviewer))
        )

        total = query.count()
        literature = query.offset((page - 1) * per_page).limit(per_page).all()

        return {
            "items": [item.to_dict() for item in literature],
            "total": total,
            "page": page,
            "per_page": per_page,
            "total_pages": -(-total // per_page),
        }

    async def process_literature(
        self,
        research_topic: str,
        categories: str,
        reviewer_id: LLMProviderType,
        lit_filter: LiteratureFilter,
    ) -> Dict[str, Any]:
        reviewer = self.db.query(Reviewer).filter(Reviewer.id == reviewer_id).first()
        only_abstract = lit_filter.review_type == ReviewType.ABSTRACT
        UnreviewedDecisions = aliased(ReviewDecision)

        query = (
            self.db.query(Literature)
            .outerjoin(
                UnreviewedDecisions,
                (Literature.id == UnreviewedDecisions.literature_id)
                & (UnreviewedDecisions.only_abstract == only_abstract)
                & (UnreviewedDecisions.reviewer_id == reviewer_id),
            )
            .filter(UnreviewedDecisions.id.is_(None))
        )

        lit_filter.review_type = None

        query = self._apply_filters(query, lit_filter)
        papers = query.all()
        total_papers = len(papers)
        processed_papers = 0

        print(f"Process {total_papers} papers")

        batch_size = Settings.SIMULTANEOUS_LLM_REQUESTS
        for i in range(0, total_papers, batch_size):
            batch = papers[i : i + batch_size]

            tasks = [
                self._process_single_paper(
                    paper,
                    research_topic,
                    categories,
                    reviewer.model_id,
                    reviewer.id,
                    only_abstract,
                )
                for paper in batch
            ]
            await asyncio.gather(*tasks)
            processed_papers += len(batch)

            progress = {
                "total": total_papers,
                "processed": processed_papers,
            }
            print(f"{processed_papers} / {total_papers}")

            # You can emit this progress through your preferred method (WebSocket, SSE, etc.)

        return {
            "status": "completed",
            "message": f"Processing completed using {reviewer_id} model",
            "total_processed": total_papers,
        }

    async def retrieve_full_texts(self, lit_fitler):
        query = self.db.query(Literature).filter(
            Literature.full_text_available.is_(False),
            Literature.doi.isnot(None),
            Literature.doi != "",
        )

        query = self._apply_filters(query, lit_fitler)
        papers = query.all()

        print(f"Getting full text for {len(papers)} papers")

        for paper in papers:
            try:
                print(f"Get full text for {paper.doi}")
                pdf_text = await self.pdf_handler.get_full_text(paper.doi)
                if len(pdf_text) > 0:
                    paper.full_text_available = True
                    paper.updated_at = func.now()
                    self.db.commit()

            except Exception as e:
                print(f"Error retrieving full text for DOI {paper.doi}: {e}")
                continue

    async def retrieve_single_full_text(self, literature_id: int) -> Dict[str, Any]:
        """
        Retrieve full text for a single literature item by ID.

        Args:
            literature_id: The ID of the literature item

        Returns:
            Dictionary with status and message
        """
        literature = self.db.query(Literature).filter(Literature.id == literature_id).first()

        if not literature:
            return {"success": False, "message": f"Literature with ID {literature_id} not found"}

        if not literature.doi:
            return {"success": False, "message": "Literature item does not have a DOI"}

        if literature.full_text_available:
            return {"success": True, "message": "Full text is already available for this item"}

        try:
            print(f"Getting full text for {literature.doi}")
            pdf_text = await self.pdf_handler.get_full_text(literature.doi)

            if len(pdf_text) > 0:
                literature.full_text_available = True
                literature.updated_at = func.now()
                self.db.commit()

                return {
                    "success": True,
                    "message": f"Successfully retrieved full text for '{literature.title}'",
                    "item": literature.to_dict()
                    if hasattr(literature, "to_dict")
                    else {
                        "id": literature.id,
                        "title": literature.title,
                        "full_text_available": literature.full_text_available,
                    },
                }
            else:
                return {"success": False, "message": f"Could not find full text for DOI: {literature.doi}"}

        except Exception as e:
            print(f"Error retrieving full text for DOI {literature.doi}: {e}")
            return {"success": False, "message": f"Error retrieving full text: {str(e)}"}

    async def save_uploaded_pdf(self, literature_id: int, pdf_content: bytes) -> Literature:
        literature = self.db.query(Literature).filter(Literature.id == literature_id).first()
        if not literature:
            raise LiteratureNotFoundError(f"Literature with id {literature_id} not found")

        pdf_dir = Path(Settings.PDF_DIRECTORY)
        pdf_dir.mkdir(exist_ok=True)

        safe_filename = self.pdf_handler.get_pdf_name(literature.doi)
        pdf_path = pdf_dir / safe_filename

        # Save the PDF file
        with open(pdf_path, "wb") as f:
            f.write(pdf_content)

        literature.full_text_available = True
        literature.updated_at = func.now()
        self.db.commit()

        return literature

    def _apply_filters(
        self,
        query,
        lit_filter: LiteratureFilter,
    ):
        query = query.filter(Literature.project_id == lit_filter.project_id)

        has_review_decision_join = False

        # Determine if we're filtering by abstract or full text reviews
        only_abstract = None
        if lit_filter.review_type:
            if lit_filter.review_type == ReviewType.FULL_PAPER:
                has_review_decision_join = True
                only_abstract = False
                query = query.join(ReviewDecision).filter(ReviewDecision.only_abstract.is_(False))
            elif lit_filter.review_type == ReviewType.ABSTRACT:
                has_review_decision_join = True
                only_abstract = True
                query = query.join(ReviewDecision).filter(ReviewDecision.only_abstract.is_(True))

        # Handle review status filtering
        if lit_filter.review_statuses and len(lit_filter.review_statuses) > 0:
            # If we have multiple review statuses, use OR condition
            if not has_review_decision_join:
                query = query.join(ReviewDecision)
                has_review_decision_join = True

            # Create an OR condition for all the review statuses
            status_conditions = []
            for status in lit_filter.review_statuses:
                status_conditions.append(ReviewDecision.decision == status.upper())

            # Apply the OR condition
            query = query.filter(or_(*status_conditions))
        elif lit_filter.review_status:
            # For backward compatibility
            if not has_review_decision_join:
                query = query.join(ReviewDecision)
                has_review_decision_join = True
            query = query.filter(ReviewDecision.decision == lit_filter.review_status.upper())

        if lit_filter.paper_status:
            if lit_filter.paper_status == "no_doi":
                query = query.filter(or_(Literature.doi.is_(None), Literature.doi == ""))
            if lit_filter.paper_status == "full_text_available":
                query = query.filter(Literature.full_text_available.is_(True))
            elif lit_filter.paper_status == "full_text_unavailable":
                query = query.filter(Literature.full_text_available.is_(False))

        if lit_filter.search:
            search_term = f"%{lit_filter.search.lower()}%"
            query = query.filter(
                Literature.title.ilike(search_term)
                | Literature.abstract.ilike(search_term)
                | Literature.authors.ilike(search_term)
                | Literature.doi.ilike(search_term)
            )

        if lit_filter.journal:
            search_term = f"%{lit_filter.journal.lower()}%"
            query = query.filter(Literature.journal.ilike(search_term))

        if lit_filter.publisher:
            search_term = f"%{lit_filter.publisher.lower()}%"
            query = query.filter(Literature.publisher.ilike(search_term))

        if lit_filter.category_filters:
            for idx, cat_filter in enumerate(lit_filter.category_filters):
                if len(cat_filter.category) == 0 or len(cat_filter.values) == 0:
                    continue

                # Create an alias for each category filter to avoid conflicts
                CategoryAlias = aliased(LiteratureCategory, name=f"category_{idx}")

                base_category_filter = [
                    CategoryAlias.category == cat_filter.category,
                    CategoryAlias.value.in_(cat_filter.values),
                ]

                # Add from_abstract filter based on review_type if specified
                if only_abstract is not None:
                    base_category_filter.append(CategoryAlias.from_abstract.is_(only_abstract))

                if hasattr(cat_filter, "exclude") and cat_filter.exclude:
                    # 1. Get all literature IDs that have the category with excluded values
                    excluded_lit_ids = self.db.query(CategoryAlias.literature_id).filter(*base_category_filter).subquery()

                    # 2. Filter out those literature IDs from the main query
                    query = query.filter(~Literature.id.in_(select(excluded_lit_ids.c.literature_id).scalar_subquery()))

                    # 3. But ensure we still only get literature that has this category
                    join_conditions = [
                        (Literature.id == CategoryAlias.literature_id),
                        (CategoryAlias.category == cat_filter.category),
                    ]

                    # Add from_abstract filter to join condition if we're filtering by review type
                    if only_abstract is not None:
                        join_conditions.append(CategoryAlias.from_abstract.is_(only_abstract))

                    # Important: Use join with explicit ON clause to avoid cartesian product
                    query = query.join(CategoryAlias, and_(*join_conditions))
                else:
                    # Important: Always use explicit join condition with and_() to prevent cartesian products
                    join_conditions = [
                        (Literature.id == CategoryAlias.literature_id),
                        (CategoryAlias.category == cat_filter.category),
                        (CategoryAlias.value.in_(cat_filter.values)),
                    ]

                    # Add from_abstract filter to join condition if needed
                    if only_abstract is not None:
                        join_conditions.append(CategoryAlias.from_abstract.is_(only_abstract))

                    query = query.join(CategoryAlias, and_(*join_conditions))

                if lit_filter.reviewer_filters:
                    reviewer_ids = json.loads(lit_filter.reviewer_filters)
                    if reviewer_ids and len(reviewer_ids) > 0:
                        if not has_review_decision_join:
                            query = query.join(ReviewDecision)
                            has_review_decision_join = True
                        query = query.filter(CategoryAlias.reviewer_id.in_(reviewer_ids))
        # Add new handling for reviewer_decision_filters
        if lit_filter.reviewer_decision_filters:
            for idx, reviewer_filter in enumerate(lit_filter.reviewer_decision_filters):
                # Create an alias for each reviewer decision join to avoid conflicts
                alias_name = f"review_decision_{idx}"
                ReviewDecisionAlias = aliased(ReviewDecision, name=alias_name)

                # Join with the aliased table
                query = query.outerjoin(
                    ReviewDecisionAlias,
                    (Literature.id == ReviewDecisionAlias.literature_id)
                    & (ReviewDecisionAlias.reviewer_id == reviewer_filter.reviewer_id),
                )

                # Add filter for decision if provided
                if reviewer_filter.decision:
                    if reviewer_filter.decision_operator == "equals":
                        query = query.filter(ReviewDecisionAlias.decision == reviewer_filter.decision.upper())
                    elif reviewer_filter.decision_operator == "not_equals":
                        # Include both cases: different decision OR no decision
                        query = query.filter(
                            (ReviewDecisionAlias.decision != reviewer_filter.decision.upper())
                            | (ReviewDecisionAlias.id.is_(None))
                        )

        # Keep existing reviewer_filters logic for backward compatibility
        if lit_filter.reviewer_filters:
            reviewer_ids = json.loads(lit_filter.reviewer_filters)
            if reviewer_ids:
                if not has_review_decision_join:
                    query = query.join(ReviewDecision)
                    has_review_decision_join = True
                query = query.filter(ReviewDecision.reviewer_id.in_(reviewer_ids))
        return query

    async def _process_single_paper(
        self,
        paper: Literature,
        research_topic,
        categories,
        llm_provider,
        reviewer_id,
        only_abstracts,
    ):
        if not only_abstracts and not paper.full_text_available:
            print("Full-Text not available not found")
            return None

        content = paper.title + "\n" + paper.abstract if only_abstracts else self.get_full_text(paper.doi)

        try:
            prompt = get_prompt(
                research_topic=research_topic,
                categories=categories,
                content=content,
                is_full_text=not only_abstracts,
                title=paper.title,
                abstract=paper.abstract,
            )
            llm = get_llm_provider(llm_provider)
            llm_response = await llm.generate_response(prompt)

            if llm_response is not None:
                self._save_review_decision(paper.id, reviewer_id, llm_response, only_abstracts)
                self._save_categories(paper.id, reviewer_id, llm_response["categories"], only_abstracts)
        except ValueError:
            print("Promt too long")

        return paper.id

    def _save_review_decision(
        self,
        literature_id: int,
        reviewer_id: int,
        llm_response: dict,
        only_abstract: bool,
    ):
        review_decision = ReviewDecision(
            literature_id=literature_id,
            reviewer_id=reviewer_id,
            decision=llm_response["decision"].upper(),
            reason=llm_response["reason"],
            only_abstract=only_abstract,
        )
        self.db.add(review_decision)
        self.db.commit()

    def _flatten_categories(self, categories):
        flatten_categories = []
        for category, value in categories.items():
            if isinstance(value, list):
                fc = [(category, v) for v in value]
            elif isinstance(value, dict):
                fc = self._flatten_categories(value)
            else:
                fc = [(category, value)]
            # flatten_categories[
            for kv in fc:
                flatten_categories.append(kv)
        return flatten_categories

    def _save_categories(
        self,
        literature_id: int,
        reviewer_id: int,
        categories: dict,
        from_abstract: bool,
    ):
        categories = self._flatten_categories(categories)

        for c, v in categories:
            literature_category = LiteratureCategory(
                literature_id=literature_id,
                category=c,
                value=v,
                from_abstract=from_abstract,
                reviewer_id=reviewer_id,
            )
            self.db.add(literature_category)
        self.db.commit()

    def _format_literature_response(self, literature: List[Literature], total, page, per_page):
        results = []
        total_full_text_words = 0
        total_abstract_words = 0

        for lit in literature:
            # abstract_word_count = len(lit.abstract.split()) if lit.abstract else 0
            # full_text_word_count = len(lit.full_text.split()) if lit.full_text else 0
            # total_abstract_words += abstract_word_count
            # total_full_text_words += full_text_word_count

            lit_dict = {
                "id": lit.id,
                "title": lit.title,
                "authors": lit.authors,
                "abstract": lit.abstract,
                # "abstract_word_count": abstract_word_count,
                # "full_text_word_count": full_text_word_count,
                "journal": lit.journal,
                "publisher": lit.publisher,
                "year": lit.year,
                "doi": lit.doi,
                "full_text_available": lit.full_text_available,
                "categories": [
                    {
                        "category": cat.category,
                        "value": cat.value,
                        "from_abstract": cat.from_abstract,
                    }
                    for cat in lit.categories
                ],
                "review_decisions": [
                    {
                        "id": dec.id,
                        "decision": dec.decision,
                        "reason": dec.reason,
                        "reviewer_name": dec.reviewer.name,
                        "created_at": dec.created_at,
                    }
                    for dec in lit.review_decisions
                ],
            }
            results.append(lit_dict)

        return {
            "items": results,
            "total": total,
            "page": page,
            "per_page": per_page,
            "total_pages": -(-total // per_page),
            "total_abstract_words": total_abstract_words,
            "total_full_text_words": total_full_text_words,
        }

    def get_literature_for_export(self, lit_filter: LiteratureFilter) -> List[Literature]:
        query = self.db.query(Literature).distinct()
        query = self._apply_filters(query, lit_filter)
        return query.all()

    def get_full_text(self, doi: str):
        pdf_handler = PDFHandler()
        return pdf_handler._get_pdf_content_if_exists(doi)

    def get_literature_without_dois(self, lit_filter: LiteratureFilter) -> Dict[str, Any]:
        """Get literature entries without DOIs for a specific project."""
        query = self.db.query(Literature).distinct()

        # Apply the project_id filter
        # query = query.filter(
        #     Literature.project_id == lit_filter.project_id,
        #     (Literature.doi.is_(None) | Literature.doi == ""),
        # )

        # Apply reviewer decision filters if provided
        query = self._apply_filters(query, lit_filter)

        # Get the results
        literature = query.all()
        total = len(literature)

        return self._format_literature_response(literature, total, 1, max(total, 1))

    def find_potential_doi_matches(self, project_id: int, literature_ids: List[int]) -> Dict[int, List[Dict[str, Any]]]:
        """Find potential DOI matches for literature entries without DOIs."""
        # Get literature entries without DOIs
        literature_without_dois = self.db.query(Literature).filter(Literature.id.in_(literature_ids)).all()

        # Dictionary to store potential matches for each literature entry
        potential_matches = {}

        for lit in literature_without_dois:
            # Find literature entries with DOIs that have similar titles or authors
            # across all projects
            matches = (
                self.db.query(Literature)
                .filter(
                    Literature.doi.isnot(None),
                    Literature.doi != "",
                    (
                        func.lower(Literature.title) == lit.title.lower()
                        and func.lower(Literature.authors).like(f"%{lit.authors.lower() if lit.authors else ''}%")
                    ),
                )
                .limit(5)  # Limit to 5 potential matches per literature
                .all()
            )

            if matches:
                # Convert matches to dictionaries for JSON response
                potential_matches[lit.id] = [
                    {
                        "id": match.id,
                        "title": match.title,
                        "authors": match.authors,
                        "journal": match.journal,
                        "year": match.year,
                        "doi": match.doi,
                    }
                    for match in matches
                ]

        return potential_matches

    def _process_literature_item(self, lit, project_id, processed_ids):
        """Process a single literature item to find duplicates.

        This is a helper method for parallel processing.
        """
        # Skip if we've already processed this literature item as part of a duplicate group
        if lit.id in processed_ids:
            return None, set()

        # Find potential duplicates based on similar titles
        similar_title_query = self.db.query(
            Literature
        ).filter(
            Literature.project_id == project_id,
            Literature.id != lit.id,  # Exclude the current item
            func.lower(Literature.title).like(f"%{lit.title.lower()[:30]}%"),  # Match on first 30 chars of title
        )

        # Add filter for similar authors if authors exist
        if lit.authors:
            # Get the first author's last name
            first_author = lit.authors.split(",")[0].strip() if "," in lit.authors else lit.authors.strip()
            last_name = first_author.split()[-1] if " " in first_author else first_author

            if len(last_name) > 3:  # Only use author filter if last name is meaningful
                similar_title_query = similar_title_query.filter(func.lower(Literature.authors).like(f"%{last_name.lower()}%"))

        similar_title_matches = similar_title_query.all()

        if not similar_title_matches:
            return None, set()

        # Create a duplicate group
        duplicate_group = {
            "original": {
                "id": lit.id,
                "title": lit.title,
                "authors": lit.authors,
                "journal": lit.journal,
                "year": lit.year,
                "doi": lit.doi,
                "full_text_available": lit.full_text_available,
            },
            "duplicates": [
                {
                    "id": match.id,
                    "title": match.title,
                    "authors": match.authors,
                    "journal": match.journal,
                    "year": match.year,
                    "doi": match.doi,
                    "full_text_available": match.full_text_available,
                    "similarity": self._calculate_similarity(lit.title.lower(), match.title.lower()),
                }
                for match in similar_title_matches
            ],
        }

        # Sort duplicates by similarity score in descending order
        duplicate_group["duplicates"].sort(key=lambda x: x["similarity"], reverse=True)

        # Only include groups with at least one duplicate with similarity > 0.6
        if any(dup["similarity"] > 0.6 for dup in duplicate_group["duplicates"]):
            # Create a set of processed IDs
            ids_to_mark = {lit.id}
            ids_to_mark.update(match.id for match in similar_title_matches)
            return duplicate_group, ids_to_mark

        return None, set()

    def find_duplicates(self, project_id: int, limit: int = 12000, offset: int = 0, chunk_size: int = 1000) -> Dict[str, Any]:
        """Find potential duplicate entries within the same project.

        Args:
            project_id: The ID of the project to search for duplicates in
            limit: Maximum number of papers to check for duplicates (default: 12000)
            offset: Number of papers to skip (for pagination/chunking)
            chunk_size: Size of each processing chunk (default: 1000)

        Returns:
            Dictionary with duplicate groups and total count
        """
        # Get total count of literature entries for the project
        total_count = self.db.query(func.count(Literature.id)).filter(Literature.project_id == project_id).scalar()

        # Adjust limit if it exceeds total count
        if limit > total_count:
            limit = total_count

        # Calculate effective limit based on offset and chunk_size
        effective_limit = min(chunk_size, limit)

        # Get literature entries for the current chunk
        literature = (
            self.db.query(Literature)
            .filter(Literature.project_id == project_id)
            .order_by(Literature.created_at.desc())
            .offset(offset)
            .limit(effective_limit)
            .all()
        )

        total_papers = len(literature)
        current_chunk = offset // chunk_size + 1
        chunk_end = min(offset + effective_limit, total_count)

        print(f"Processing {total_papers} papers in chunk {current_chunk} ({offset + 1}-{chunk_end} of {total_count})")

        # Dictionary to store groups of potential duplicates
        duplicate_groups = []
        processed_ids = set()

        # Process literature items in parallel using a thread pool
        # Determine the number of workers based on CPU cores
        num_workers = min(32, os.cpu_count() * 2)
        print(f"Using {num_workers} workers for parallel processing")

        # Process in smaller batches to show progress
        batch_size = 50
        batches = [literature[i : i + batch_size] for i in range(0, len(literature), batch_size)]

        for batch_idx, batch in enumerate(batches):
            print(f"Processing batch {batch_idx + 1}/{len(batches)} ({len(batch)} items)")

            # Filter out items that have already been processed
            batch_to_process = [lit for lit in batch if lit.id not in processed_ids]

            if not batch_to_process:
                print(f"Skipping batch {batch_idx + 1} - all items already processed")
                continue

            # Process the batch in parallel
            with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
                # Submit all tasks
                future_to_lit = {
                    executor.submit(self._process_literature_item, lit, project_id, processed_ids): lit
                    for lit in batch_to_process
                }

                # Process results as they complete
                for future in concurrent.futures.as_completed(future_to_lit):
                    lit = future_to_lit[future]
                    try:
                        duplicate_group, ids_to_mark = future.result()
                        if duplicate_group:
                            duplicate_groups.append(duplicate_group)
                            processed_ids.update(ids_to_mark)
                    except Exception as exc:
                        print(f"Processing item {lit.id} generated an exception: {exc}")

            print(f"Completed batch {batch_idx + 1} - found {len(duplicate_groups)} duplicate groups so far")

        return {
            "duplicate_groups": duplicate_groups,
            "total_groups": len(duplicate_groups),
            "papers_checked": total_papers,
            "limit_applied": limit,
            "offset": offset,
            "chunk_size": chunk_size,
            "current_chunk": current_chunk,
            "total_count": total_count,
            "has_more": offset + effective_limit < min(total_count, limit),
            "next_offset": offset + effective_limit if offset + effective_limit < min(total_count, limit) else None,
        }

    async def lookup_doi_by_title(self, title: str, authors: str = None) -> Dict[str, Any]:
        """
        Look up a DOI by paper title using external APIs.

        This method tries multiple sources:
        1. Crossref API
        2. DataCite API
        3. Semantic Scholar API

        Returns a dictionary with found status and DOI if found.
        """
        # Clean the title for better matching
        clean_title = re.sub(r"[^\w\s]", "", title).lower()

        # Try Crossref first (most comprehensive)
        crossref_result = await self._lookup_doi_crossref(clean_title, authors)
        if crossref_result.get("found"):
            return crossref_result

        # Try DataCite as fallback
        datacite_result = await self._lookup_doi_datacite(clean_title, authors)
        if datacite_result.get("found"):
            return datacite_result

        # Try Semantic Scholar as last resort
        semantic_result = await self._lookup_doi_semantic_scholar(clean_title, authors)
        if semantic_result.get("found"):
            return semantic_result

        # No DOI found in any source
        return {
            "found": False,
            "message": "No DOI found for this title in any of the external sources",
        }

    async def _lookup_doi_crossref(self, title: str, authors: str = None) -> Dict[str, Any]:
        """Look up a DOI using the Crossref API."""
        encoded_title = quote(title)

        # Build the URL with author information if available
        url = f"https://api.crossref.org/works?query.title={encoded_title}&rows=10"
        if authors:
            # Extract first author's last name for better matching
            first_author = authors.split(",")[0].strip() if "," in authors else authors.strip()
            last_name = first_author.split()[-1] if " " in first_author else first_author
            encoded_author = quote(last_name)
            url = f"{url}&query.author={encoded_author}"

        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        items = data.get("message", {}).get("items", [])

                        if items:
                            # Find the best match by comparing titles and authors
                            best_match = None
                            highest_score = 0

                            for item in items:
                                item_title = item.get("title", [""])[0].lower() if item.get("title") else ""
                                similarity = self._calculate_similarity(title, item_title)

                                # If authors are provided, check author match too
                                if authors and similarity > 0.7:
                                    item_authors = item.get("author", [])
                                    author_names = []
                                    for author in item_authors:
                                        if "family" in author:
                                            family = author.get("family", "")
                                            given = author.get("given", "")
                                            if given:
                                                author_names.append(f"{given} {family}")
                                            else:
                                                author_names.append(family)

                                    # Check if any of the provided authors match
                                    author_match_score = 0
                                    if author_names:
                                        author_text = ", ".join(author_names).lower()
                                        # Check for partial matches in either direction
                                        if any(author.lower() in authors.lower() for author in author_names) or any(
                                            author_part.lower() in author_text for author_part in authors.lower().split(",")
                                        ):
                                            author_match_score = 0.15  # Significant boost for author match

                                    similarity += author_match_score

                                if similarity > highest_score:
                                    highest_score = similarity
                                    best_match = item

                            # If we have a good match (similarity > 0.8)
                            if best_match and highest_score > 0.8:
                                # Extract author names for the response
                                author_list = []
                                for author in best_match.get("author", []):
                                    if "family" in author:
                                        family = author.get("family", "")
                                        given = author.get("given", "")
                                        if given:
                                            author_list.append(f"{given} {family}")
                                        else:
                                            author_list.append(family)

                                return {
                                    "found": True,
                                    "doi": best_match.get("DOI"),
                                    "title": best_match.get("title", [""])[0],
                                    "authors": ", ".join(author_list),
                                    "score": highest_score,
                                    "source": "Crossref",
                                }

                    return {
                        "found": False,
                        "message": "No matching DOI found in Crossref",
                    }

            except Exception as e:
                print(f"Error querying Crossref: {e}")
                return {"found": False, "error": str(e)}

    async def _lookup_doi_datacite(self, title: str, authors: str = None) -> Dict[str, Any]:
        """Look up a DOI using the DataCite API."""
        encoded_title = quote(title)

        # Build the URL with author information if available
        url = f"https://api.datacite.org/works?query={encoded_title}"
        if authors:
            # Extract first author's last name for better matching
            first_author = authors.split(",")[0].strip() if "," in authors else authors.strip()
            last_name = first_author.split()[-1] if " " in first_author else first_author
            encoded_author = quote(last_name)
            # DataCite doesn't have a specific author query parameter, so we add it to the general query
            url = f"https://api.datacite.org/works?query={encoded_title}+{encoded_author}"

        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        items = data.get("data", [])

                        if items:
                            best_match = None
                            highest_score = 0

                            for item in items:
                                attributes = item.get("attributes", {})
                                item_title = attributes.get("title", "").lower()
                                similarity = self._calculate_similarity(title, item_title)

                                # If authors are provided, check author match too
                                if authors and similarity > 0.7:
                                    # DataCite stores authors in the 'creator' field
                                    item_creators = attributes.get("creator", [])
                                    if item_creators:
                                        creator_names = []
                                        for creator in item_creators:
                                            if isinstance(creator, str):
                                                creator_names.append(creator)
                                            elif isinstance(creator, dict) and "name" in creator:
                                                creator_names.append(creator["name"])

                                        # Check if any of the provided authors match
                                        if creator_names:
                                            creators_text = ", ".join(creator_names).lower()
                                            # Check for partial matches in either direction
                                            if any(creator.lower() in authors.lower() for creator in creator_names) or any(
                                                author_part.lower() in creators_text
                                                for author_part in authors.lower().split(",")
                                            ):
                                                similarity += 0.15  # Boost score if author matches

                                if similarity > highest_score:
                                    highest_score = similarity
                                    best_match = item

                            if best_match and highest_score > 0.8:
                                attributes = best_match.get("attributes", {})

                                # Extract author names for the response
                                author_list = []
                                for creator in attributes.get("creator", []):
                                    if isinstance(creator, str):
                                        author_list.append(creator)
                                    elif isinstance(creator, dict) and "name" in creator:
                                        author_list.append(creator["name"])

                                return {
                                    "found": True,
                                    "doi": attributes.get("doi"),
                                    "title": attributes.get("title"),
                                    "authors": ", ".join(author_list),
                                    "score": highest_score,
                                    "source": "DataCite",
                                }

                    return {
                        "found": False,
                        "message": "No matching DOI found in DataCite",
                    }

            except Exception as e:
                print(f"Error querying DataCite: {e}")
                return {"found": False, "error": str(e)}

    async def _lookup_doi_semantic_scholar(self, title: str, authors: str = None) -> Dict[str, Any]:
        """Look up a DOI using the Semantic Scholar API."""
        encoded_title = quote(title)

        # Build the URL with author information if available
        url = f"https://api.semanticscholar.org/graph/v1/paper/search?query={encoded_title}&fields=title,authors,externalIds"
        if authors:
            # Extract first author's last name for better matching
            first_author = authors.split(",")[0].strip() if "," in authors else authors.strip()
            last_name = first_author.split()[-1] if " " in first_author else first_author
            encoded_author = quote(last_name)
            # Add author to the query
            url = f"https://api.semanticscholar.org/graph/v1/paper/search?query={encoded_title}+{encoded_author}&fields=title,authors,externalIds"

        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        items = data.get("data", [])

                        if items:
                            best_match = None
                            highest_score = 0

                            for item in items:
                                item_title = item.get("title", "").lower()
                                similarity = self._calculate_similarity(title, item_title)

                                # If authors are provided, check author match too
                                if authors and similarity > 0.7:
                                    item_authors = item.get("authors", [])
                                    if item_authors:
                                        author_names = []
                                        for author in item_authors:
                                            name = author.get("name", "")
                                            if name:
                                                author_names.append(name)

                                        # Check if any of the provided authors match
                                        if author_names:
                                            authors_text = ", ".join(author_names).lower()
                                            # Check for partial matches in either direction
                                            if any(author.lower() in authors.lower() for author in author_names) or any(
                                                author_part.lower() in authors_text
                                                for author_part in authors.lower().split(",")
                                            ):
                                                similarity += 0.15  # Boost score if author matches

                                if similarity > highest_score:
                                    highest_score = similarity
                                    best_match = item

                            if best_match and highest_score > 0.8:
                                external_ids = best_match.get("externalIds", {})
                                doi = external_ids.get("DOI")

                                if doi:
                                    # Extract author names for the response
                                    author_list = []
                                    for author in best_match.get("authors", []):
                                        name = author.get("name", "")
                                        if name:
                                            author_list.append(name)

                                    return {
                                        "found": True,
                                        "doi": doi,
                                        "title": best_match.get("title"),
                                        "authors": ", ".join(author_list),
                                        "score": highest_score,
                                        "source": "Semantic Scholar",
                                    }

                    return {
                        "found": False,
                        "message": "No matching DOI found in Semantic Scholar",
                    }

            except Exception as e:
                print(f"Error querying Semantic Scholar: {e}")
                return {"found": False, "error": str(e)}

    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """
        Calculate string similarity between two strings.
        Returns a score between 0 and 1, where 1 is a perfect match.
        """
        # Simple implementation using difflib
        import difflib

        # Normalize strings
        str1 = str1.lower().strip()
        str2 = str2.lower().strip()

        # Calculate similarity ratio
        return difflib.SequenceMatcher(None, str1, str2).ratio()
