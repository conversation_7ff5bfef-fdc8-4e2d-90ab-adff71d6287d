import React, { useState, useEffect, useCallback } from 'react';
import { Literature, ReviewerCategoryColumn } from '../types';
import { LiteratureItem } from './LiteratureItem.tsx';
import { literatureService } from '../services/literatureService.ts';
import { categoryService } from '../services/categoryService.ts';
import { useFilters } from '../context/FilterContext.tsx';
import { useReviewer } from '../context/ReviewerContext.tsx';

const PER_PAGE_OPTIONS = [
    { value: 1, label: '1' },
    { value: 10, label: '10' },
    { value: 100, label: '100' },
    { value: 200, label: '200' },
    { value: -1, label: 'All' },
];

interface LiteratureListProps {
    review?: boolean;
}
export function LiteratureList({ review }: LiteratureListProps) {
    const { lit_filter, setLitFilter, resetFilters } = useFilters();
    const [items, setItems] = useState<Literature[]>([]);
    const [total, setTotal] = useState(0);
    const [totalPages, setTotalPages] = useState(0);
    const [selectedReviewer, setSelectedReviewer] = useState<number>(0);

    const [totalAbstractWords, setTotalAbstractWords] = useState(0);
    const [totalFullTextWords, setTotalFullTextWords] = useState(0);
    const [isExporting, setIsExporting] = useState(false);

    const [showExportModal, setShowExportModal] = useState(false);
    const [selectedColumns, setSelectedColumns] = useState<string[]>(['id', 'doi', 'title']);
    const [selectedReviewerCategoryColumns, setSelectedReviewerCategoryColumns] = useState<
        ReviewerCategoryColumn[]
    >([]);
    const [availableCategories, setAvailableCategories] = useState<string[]>([]);
    const { currentReviewer } = useReviewer();

    const fetchLiterature = useCallback(
        async (currentPage: number) => {
            try {
                const data = await literatureService.getLiterature({
                    page: currentPage,
                    perPage: lit_filter.per_page || 10,
                    lit_filter: {
                        ...lit_filter,
                        page: currentPage,
                    },
                });
                setItems(data.items);
                setTotal(data.total);
                setTotalPages(data.total_pages);
                setTotalAbstractWords(data.total_abstract_words);
                setTotalFullTextWords(data.total_full_text_words);
            } catch (error) {
                console.error('Error fetching literature:', error);
            }
        },
        [lit_filter]
    );

    useEffect(() => {
        const timer = setTimeout(() => {
            fetchLiterature(lit_filter.page || 1);
        }, 300); // 300ms debounce

        return () => clearTimeout(timer);
    }, [lit_filter, fetchLiterature]);

    const handleReload = () => {
        fetchLiterature(lit_filter.page || 1);
    };

    const handlePageChange = (newPage: number) => {
        setLitFilter({
            ...lit_filter,
            page: newPage,
        });
    };

    const handleResetFilters = () => {
        resetFilters();
        // After resetting filters, fetch the literature with the reset filters
        fetchLiterature(1);
    };

    const handleItemUpdate = (updatedItem: Literature) => {
        setItems(items.map((item) => (item.id === updatedItem.id ? updatedItem : item)));
    };

    const handleExportRIS = async () => {
        try {
            setIsExporting(true);
            const blob = await literatureService.exportAsRIS(lit_filter);
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'literature_export.ris';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (error) {
            console.error('Error exporting RIS:', error);
            alert('Failed to export RIS file');
        } finally {
            setIsExporting(false);
        }
    };

    const handleExportBibtex = async () => {
        try {
            setIsExporting(true);
            const blob = await literatureService.exportAsBibtex(lit_filter);
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'literature_export.bib';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (error) {
            console.error('Error exporting BibTeX:', error);
            alert('Failed to export BibTeX file');
        } finally {
            setIsExporting(false);
        }
    };

    const handleExportCSV = async () => {
        try {
            setIsExporting(true);

            // Get reviewer IDs from selected columns
            const reviewerColumns = selectedColumns.filter((col) => col.startsWith('reviewer_'));
            const reviewerIds = reviewerColumns.map((col) => parseInt(col.split('_')[1]));

            const blob = await literatureService.exportAsCSV(
                lit_filter,
                selectedColumns,
                reviewerIds,
                selectedReviewerCategoryColumns
            );
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'literature_export.csv';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            setShowExportModal(false);
        } catch (error) {
            console.error('Error exporting CSV:', error);
            alert('Failed to export CSV file');
        } finally {
            setIsExporting(false);
        }
    };

    const toggleColumn = (column: string) => {
        if (selectedColumns.includes(column)) {
            setSelectedColumns(selectedColumns.filter((col) => col !== column));
        } else {
            setSelectedColumns([...selectedColumns, column]);
        }
    };

    const fetchCategories = async () => {
        try {
            const categories = await categoryService.getCategories(lit_filter);
            setAvailableCategories(categories);
        } catch (error) {
            console.error('Error fetching categories:', error);
        }
    };

    const addReviewerCategoryColumn = (reviewerId: number, category: string) => {
        const exists = selectedReviewerCategoryColumns.some(
            (col) => col.reviewer_id === reviewerId && col.category === category
        );
        if (!exists) {
            setSelectedReviewerCategoryColumns([
                ...selectedReviewerCategoryColumns,
                { reviewer_id: reviewerId, category: category },
            ]);
        }
    };

    const removeReviewerCategoryColumn = (reviewerId: number, category: string) => {
        setSelectedReviewerCategoryColumns(
            selectedReviewerCategoryColumns.filter(
                (col) => !(col.reviewer_id === reviewerId && col.category === category)
            )
        );
    };

    const handleExportModalOpen = () => {
        setShowExportModal(true);
        fetchCategories();
    };

    return (
        <div className="literature-list">
            <h2>Literature Items ({total} total)</h2>
            {!review && (
                <div className="word-count-summary">
                    <p>Total Abstract Words: {totalAbstractWords?.toLocaleString()}</p>
                    <p>Total Full Text Words: {totalFullTextWords?.toLocaleString()}</p>
                </div>
            )}

            <div className="filter-controls">
                <select
                    value={lit_filter.per_page || 10}
                    onChange={(e) => {
                        setLitFilter({
                            ...lit_filter,
                            page: 1,
                            per_page: Number(e.target.value),
                        });
                        handleReload();
                    }}
                    className="per-page-select">
                    {PER_PAGE_OPTIONS.map((option) => (
                        <option key={option.value} value={option.value}>
                            {option.label} per page
                        </option>
                    ))}
                </select>
                <button onClick={handleReload} className="reload-button">
                    🔄 Reload
                </button>
                <button onClick={handleResetFilters} className="reset-filters-button">
                    🗑️ Reset Filters
                </button>

                {/* Export buttons */}
                <div className="export-buttons">
                    <button
                        onClick={handleExportRIS}
                        disabled={isExporting || total === 0}
                        className="export-button">
                        {isExporting ? 'Exporting...' : 'Export as RIS'}
                    </button>
                    <button
                        onClick={handleExportBibtex}
                        disabled={isExporting || total === 0}
                        className="export-button">
                        {isExporting ? 'Exporting...' : 'Export as BibTeX'}
                    </button>
                    <button
                        onClick={handleExportModalOpen}
                        disabled={isExporting || total === 0}
                        className="export-button">
                        Export as CSV
                    </button>
                </div>
            </div>

            {items.map((item) => (
                <LiteratureItem
                    key={item.id}
                    item={item}
                    review={review}
                    selectedReviewer={selectedReviewer}
                    onReviewerSelect={setSelectedReviewer}
                    onItemUpdate={handleItemUpdate}
                    onReviewNext={review ? () => handlePageChange((lit_filter.page || 1) + 1) : undefined}
                />
            ))}

            <div className="pagination">
                <button
                    onClick={() => handlePageChange(Math.max(1, (lit_filter.page || 1) - 1))}
                    disabled={lit_filter.page === 1}>
                    Previous
                </button>
                <span>
                    Page {lit_filter.page} of {totalPages}
                </span>
                <button
                    onClick={() => handlePageChange(Math.min(totalPages, (lit_filter.page || 1) + 1))}
                    disabled={lit_filter.page === totalPages}>
                    Next
                </button>
            </div>
            {showExportModal && (
                <div className="modal-overlay">
                    <div className="export-csv-modal">
                        <div className="modal-header">
                            <h3>Export to CSV</h3>
                            <button className="close-button" onClick={() => setShowExportModal(false)}>
                                &times;
                            </button>
                        </div>

                        <div className="modal-body">
                            <h4>Select columns to export:</h4>

                            <div className="column-section">
                                <h5>Basic Information</h5>
                                <div className="column-options">
                                    {[
                                        { id: 'id', label: 'ID' },
                                        { id: 'doi', label: 'DOI' },
                                        { id: 'title', label: 'Title' },
                                        { id: 'authors', label: 'Authors' },
                                        { id: 'journal', label: 'Journal' },
                                        { id: 'publisher', label: 'Publisher' },
                                        { id: 'year', label: 'Year' },
                                        { id: 'abstract', label: 'Abstract' },
                                        { id: 'full_text_available', label: 'Full Text Available' },
                                    ].map((column) => (
                                        <div key={column.id} className="column-option">
                                            <label>
                                                <input
                                                    type="checkbox"
                                                    checked={selectedColumns.includes(column.id)}
                                                    onChange={() => toggleColumn(column.id)}
                                                />
                                                {column.label}
                                            </label>
                                        </div>
                                    ))}
                                </div>
                            </div>

                            <div className="column-section">
                                <h5>Reviewer Decisions</h5>
                                <div className="column-options">
                                    {currentReviewer &&
                                        currentReviewer.map((reviewer) => (
                                            <div key={reviewer.id} className="column-option">
                                                <label>
                                                    <input
                                                        type="checkbox"
                                                        checked={selectedColumns.includes(
                                                            `reviewer_${reviewer.id}`
                                                        )}
                                                        onChange={() => toggleColumn(`reviewer_${reviewer.id}`)}
                                                    />
                                                    {reviewer.name} (Decision & Reason)
                                                </label>
                                            </div>
                                        ))}
                                </div>
                            </div>

                            <div className="column-section">
                                <h5>Reviewer Categories</h5>
                                <div className="reviewer-category-section">
                                    {currentReviewer &&
                                        currentReviewer.map((reviewer) => (
                                            <div key={reviewer.id} className="reviewer-category-group">
                                                <h6>{reviewer.name}</h6>
                                                <div className="category-options">
                                                    {availableCategories.map((category) => {
                                                        const isSelected = selectedReviewerCategoryColumns.some(
                                                            (col) =>
                                                                col.reviewer_id === reviewer.id &&
                                                                col.category === category
                                                        );
                                                        return (
                                                            <div key={category} className="column-option">
                                                                <label>
                                                                    <input
                                                                        type="checkbox"
                                                                        checked={isSelected}
                                                                        onChange={() => {
                                                                            if (isSelected) {
                                                                                removeReviewerCategoryColumn(
                                                                                    reviewer.id,
                                                                                    category
                                                                                );
                                                                            } else {
                                                                                addReviewerCategoryColumn(
                                                                                    reviewer.id,
                                                                                    category
                                                                                );
                                                                            }
                                                                        }}
                                                                    />
                                                                    {category}
                                                                </label>
                                                            </div>
                                                        );
                                                    })}
                                                </div>
                                            </div>
                                        ))}
                                </div>
                            </div>
                        </div>

                        <div className="modal-footer">
                            <button className="cancel-button" onClick={() => setShowExportModal(false)}>
                                Cancel
                            </button>
                            <button
                                className="export-button"
                                onClick={handleExportCSV}
                                disabled={selectedColumns.length === 0 || isExporting}>
                                {isExporting ? 'Exporting...' : 'Export CSV'}
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
