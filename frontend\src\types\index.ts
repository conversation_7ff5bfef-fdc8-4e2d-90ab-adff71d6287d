export interface Literature {
    id: number;
    title: string;
    authors: string;
    abstract: string;
    doi: string;
    review_decisions: ReviewDecision[];
    project_id: number;
    full_text_available: boolean;
    journal: string;
    publisher: string;
    year: number;
    url?: string;
    categories: {
        category: string;
        value: string;
        from_abstract: boolean;
    }[];
}

export interface ReviewDecision {
    reviewer_id: number;
    reviewer_name: string;
    decision: string;
    reason: string;
    created_at: string;
}

export interface Reviewer {
    id: number;
    name: string;
    type: string;
    model_id?: string;
}

export interface CategoryFilter {
    category: string;
    values: string[];
    exclude: boolean;
}

export interface ReviewerCategoryColumn {
    reviewer_id: number;
    category: string;
}

export type ViewMode = 'all' | 'disagreeing';

export interface FilterHistory {
    id: string;
    name: string;
    timestamp: string;
    filters: LiteratureFilter;
}

export interface Project {
    id: number;
    name: string;
    research_topic: string;
    category_description: string;
    description: string;
}

export enum ReviewType {
    FULL_PAPER = 'full_paper',
    ABSTRACT = 'abstract',
}

// Add this interface
export interface ReviewerDecisionFilter {
    reviewer_id: number;
    decision: string | null;
    decision_operator: 'equals' | 'not_equals';
}

export interface LiteratureFilter {
    project_id: number | null;
    page: number | null;
    per_page: number | null;
    review_status: string | null;
    review_statuses?: string[] | null; // New field for multiple review statuses
    paper_status: string | null;
    search: string | null;
    category_filters: CategoryFilter[] | null;
    reviewer_filters: string | null;
    reviewer_decision_filters?: ReviewerDecisionFilter[] | null;
    review_type: ReviewType | null;
    view_mode: 'all' | 'disagreeing' | null;
    journal: string | null;
    publisher: string | null;
}
