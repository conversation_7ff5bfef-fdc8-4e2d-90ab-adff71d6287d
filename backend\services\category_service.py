from sqlalchemy.orm import Session
import json
from modules.db import Literature, LiteratureCategory
from services.literature_service import LiteratureFilter, ReviewType


class CategoryService:
    def __init__(self, db: Session):
        self.db = db

    def get_categories(self, lit_filter: LiteratureFilter):
        query = self.db.query(LiteratureCategory.category).join(Literature).distinct().order_by(LiteratureCategory.category)

        query = self._apply_filters(query, lit_filter)
        categories = query.all()
        return [category[0] for category in categories]

    def get_category_values(self, category: str, lit_filter: LiteratureFilter):
        query = (
            self.db.query(LiteratureCategory.value).join(Literature).filter(LiteratureCategory.category == category).distinct()
        )

        query = self._apply_filters(query, lit_filter)

        values = query.all()
        return sorted([value[0] for value in values if value[0] is not None])

    def _apply_filters(self, query, lit_filter: LiteratureFilter):
        # Reuse the filter logic from LiteratureService
        from services.literature_service import LiteratureService

        if lit_filter.review_type:
            if lit_filter.review_type == ReviewType.ABSTRACT:
                query = query.filter(LiteratureCategory.from_abstract)
            elif lit_filter.review_type == ReviewType.FULL_PAPER:
                query = query.filter(~LiteratureCategory.from_abstract)

        if lit_filter.reviewer_filters:
            reviewer_ids = json.loads(lit_filter.reviewer_filters)
            query = query.filter(LiteratureCategory.reviewer_id.in_(reviewer_ids))

        return LiteratureService._apply_filters(self, query, lit_filter)

    def merge_categories(self, source_category: str, target_category: str):
        self.db.query(LiteratureCategory).filter(LiteratureCategory.category == source_category).update(
            {"category": target_category}
        )
        self.db.commit()
        return f"Successfully merged {source_category} into {target_category}"

    def merge_category_values(self, category: str, source_value: str, target_value: str):
        self.db.query(LiteratureCategory).filter(
            LiteratureCategory.category == category,
            LiteratureCategory.value == source_value,
        ).update({"value": target_value})
        self.db.commit()
        return f"Successfully merged value {source_value} into {target_value}"

    def fix_nested_categories(self):
        categories = self.db.query(LiteratureCategory).filter(LiteratureCategory.value.like("{%}")).all()

        for category in categories:
            try:
                value_dict = json.loads(category.value.replace("'", '"'))
                if isinstance(value_dict, dict):
                    for key, value in value_dict.items():
                        new_category = LiteratureCategory(
                            literature_id=category.literature_id,
                            category=f"{category.category}_{key}",
                            value=str(value),
                            reviewer_id=category.reviewer_id,
                        )
                        self.db.add(new_category)
                    self.db.delete(category)
            except json.JSONDecodeError:
                continue

        self.db.commit()
        return "Successfully fixed nested categories"

    def normalize_category_values(self):
        categories = self.db.query(LiteratureCategory).all()

        for category in categories:
            try:
                if category.value.startswith("[") or category.value.startswith("{"):
                    parsed_value = json.loads(category.value.replace("'", '"'))

                    if isinstance(parsed_value, list):
                        for value in parsed_value:
                            new_category = LiteratureCategory(
                                literature_id=category.literature_id,
                                category=category.category,
                                value=str(value).strip("\"'"),
                                reviewer_id=category.reviewer_id,
                            )
                            self.db.add(new_category)
                        self.db.delete(category)

                    elif isinstance(parsed_value, dict):
                        for key, value in parsed_value.items():
                            new_category = LiteratureCategory(
                                literature_id=category.literature_id,
                                category=f"{category.category}_{key}",
                                value=str(value).strip("\"'"),
                                reviewer_id=category.reviewer_id,
                            )
                            self.db.add(new_category)
                        self.db.delete(category)

                else:
                    cleaned_value = category.value.strip("\"'")
                    if cleaned_value != category.value:
                        category.value = cleaned_value

            except (json.JSONDecodeError, AttributeError):
                continue

        self.db.commit()
        return "Successfully normalized category values"
