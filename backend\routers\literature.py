import base64
import csv
from io import String<PERSON>
from pathlib import Path
from typing import Any, Dict, List, Optional


import rispy
from click import File
from routers.auth import check_tocken, get_current_user
from config import Settings
from fastapi import APIRouter, BackgroundTasks, Depends, Form, HTTPException, UploadFile
from fastapi.responses import FileResponse, Response
from modules.db import Literature, LiteratureCategory, Project, Reviewer, get_db
from modules.llm import LLMProviderType, get_llm_provider
from pybtex.database import BibliographyData, Entry
from pybtex.database.output.bibtex import Writer as BibtexWriter
from pydantic import BaseModel
from rispy import load
from services.literature_service import LiteratureFilter, LiteratureService
from sqlalchemy import func
from sqlalchemy.orm import Session

router = APIRouter(prefix="/literature", tags=["literature"])
processing_status: Dict[int, bool] = {}


def extract_journal_from_ris_entry(entry: dict) -> str:
    """
    Extract journal name from various possible RIS fields.

    Args:
        entry: A dictionary containing RIS entry data

    Returns:
        The journal name as a string, or empty string if not found
    """
    journal = entry.get("journal_name", "")
    if not journal:
        journal = entry.get("secondary_title", "")
    if not journal:
        journal = entry.get("alternate_title3", "")  # JF tag
    if not journal:
        journal = entry.get("alternate_title2", "")  # JA tag
    return journal


def extract_publisher_from_ris_entry(entry: dict) -> str:
    """
    Extract publisher name from RIS entry.

    In RIS format, PB tag is used for publisher.

    Args:
        entry: A dictionary containing RIS entry data

    Returns:
        The publisher name as a string, or empty string if not found
    """
    # In rispy, the PB tag is mapped to "publisher"
    publisher = entry.get("publisher", "")
    return publisher


class ProcessRequest(BaseModel):
    reviewer_id: int
    lit_filter: LiteratureFilter


@router.post("/list")
async def get_literature(
    lit_filter: LiteratureFilter,
    page: int = 1,
    per_page: int = Settings.DEFAULT_PAGE_SIZE,
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    service = LiteratureService(db)
    # Use keyset pagination instead of offset pagination for better performance
    return service.get_literature(
        page=page,
        per_page=per_page,
        lit_filter=lit_filter,
    )


class LiteratureUpdateRequest(BaseModel):
    title: str = None
    authors: str = None
    abstract: str = None
    journal: str = None
    publisher: str = None
    year: int = None
    doi: str = None
    keywords: str = None
    user_keywords: str = None
    full_text_available: bool = None


@router.put("/{literature_id}")
async def update_literature(
    literature_id: int,
    update_data: LiteratureUpdateRequest,
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    service = LiteratureService(db)
    literature = db.query(Literature).filter(Literature.id == literature_id).first()

    if not literature:
        raise HTTPException(status_code=404, detail="Literature not found")

    # Update fields if they are provided
    if update_data.title is not None:
        literature.title = update_data.title
    if update_data.authors is not None:
        literature.authors = update_data.authors
    if update_data.abstract is not None:
        literature.abstract = update_data.abstract
    if update_data.journal is not None:
        literature.journal = update_data.journal
    if update_data.publisher is not None:
        literature.publisher = update_data.publisher
    if update_data.year is not None:
        literature.year = update_data.year
    if update_data.doi is not None:
        literature.doi = update_data.doi
    if update_data.keywords is not None:
        literature.keywords = update_data.keywords
    if update_data.user_keywords is not None:
        literature.user_keywords = update_data.user_keywords
    if update_data.full_text_available is not None:
        literature.full_text_available = update_data.full_text_available

    literature.updated_at = func.now()
    db.commit()

    # Use the service's format method to return a serialized item
    formatted_item = service._format_literature_response([literature], 1, 1, 1)
    return {
        "message": "Literature updated successfully",
        "item": formatted_item["items"][0],
    }


@router.get("/disagreeing-reviews")
async def get_disagreeing_reviews(project_id: int, page: int = 1, per_page: int = 10, db: Session = Depends(get_db)):
    service = LiteratureService(db)
    return service.get_disagreeing_reviews(project_id, page, per_page)


@router.get("/process-status/{project_id}")
async def get_processing_status(project_id: int):
    return {"isProcessing": processing_status.get(project_id, False)}


@router.post("/process")
async def process_literature(
    request: ProcessRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
):
    service = LiteratureService(db)
    project_id = request.lit_filter.project_id
    project = db.query(Project).filter(Project.id == project_id).first()

    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    if processing_status.get(project_id, False):
        return {"message": "Processing already in progress"}

    async def process_with_status():
        try:
            await service.process_literature(
                research_topic=project.research_topic,
                categories=project.category_description,
                reviewer_id=request.reviewer_id,
                lit_filter=request.lit_filter,
            )
        finally:
            processing_status[project_id] = False

    background_tasks.add_task(process_with_status)

    return {"message": f"Processing filtered literature started using reviwer {request.reviewer_id}"}


@router.post("/retrieve-full-texts")
async def retrieve_full_texts(
    background_tasks: BackgroundTasks,
    lit_filter: LiteratureFilter,
    db: Session = Depends(get_db),
) -> Dict[str, str]:
    service = LiteratureService(db)
    background_tasks.add_task(service.retrieve_full_texts, lit_filter)

    return {"message": "Full text retrieval process started for filtered papers"}


@router.post("/retrieve-single-full-text/{literature_id}")
async def retrieve_single_full_text(
    literature_id: int,
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    """
    Retrieve full text for a single literature item by ID.
    This is a synchronous endpoint that waits for the retrieval to complete.
    """
    service = LiteratureService(db)
    result = await service.retrieve_single_full_text(literature_id)
    return result


@router.get("/pdf/{doi:path}")
async def get_pdf(doi: str, token: str):
    check_tocken(token)
    pdf_dir = Path(Settings.PDF_DIRECTORY)
    safe_filename = base64.b64encode(bytes(doi, "utf-8")).decode("utf-8") + ".pdf"
    pdf_path = pdf_dir / safe_filename

    if pdf_path.exists():
        return FileResponse(pdf_path, media_type="application/pdf", filename=f"{doi}.pdf")
    else:
        raise HTTPException(status_code=404, detail="PDF not found")


@router.post("/upload-pdf")
async def upload_pdf(literature_id: int, pdf: UploadFile = File(), db: Session = Depends(get_db)):
    if not pdf.content_type == "application/pdf":
        raise HTTPException(400, "File must be a PDF")

    content = await pdf.read()
    literature_service = LiteratureService(db)
    await literature_service.save_uploaded_pdf(literature_id, content)

    # Get the updated literature item and format it
    literature = db.query(Literature).filter(Literature.id == literature_id).first()
    formatted_item = literature_service._format_literature_response([literature], 1, 1, 1)

    return {"message": "PDF uploaded successfully", "item": formatted_item["items"][0]}


@router.post("/upload-ris")
async def upload_ris(
    file: UploadFile = File(...),
    project_id: int = Form(...),
    only_new: bool = Form(False),
    db: Session = Depends(get_db),
):
    content = await file.read()
    text_content = content.decode()

    entries = load(StringIO(text_content))

    created_count = 0
    skipped_count = 0

    print(f"File Entries: {len(entries)}")
    for entry in entries:
        doi = entry.get("doi", "")

        # If only_new is True and DOI exists, check if literature with this DOI already exists
        if only_new and doi:
            existing_lit = db.query(Literature).filter(Literature.doi == doi, Literature.project_id == project_id).first()

            if existing_lit:
                skipped_count += 1
                continue

        # Extract journal and publisher names using helper functions
        journal = extract_journal_from_ris_entry(entry)
        publisher = extract_publisher_from_ris_entry(entry)

        lit = Literature(
            project_id=project_id,
            ris_type=entry.get("type", ""),
            doi=doi,
            authors=", ".join(entry.get("authors", [])),
            title=entry.get("title", ""),
            journal=journal,
            publisher=publisher,
            year=entry.get("year"),
            abstract=entry.get("abstract", ""),
        )
        db.add(lit)
        created_count += 1

    db.commit()

    return {
        "message": "File processed successfully",
        "created": created_count,
        "skipped": skipped_count,
    }


@router.post("/upload-ris-update")
async def upload_ris_update(
    file: UploadFile = File(...),
    project_id: int = Form(...),
    db: Session = Depends(get_db),
):
    content = await file.read()
    text_content = content.decode()

    entries = load(StringIO(text_content))

    updated_count = 0
    skipped_count = 0

    print(f"File Entries for update: {len(entries)}")
    for entry in entries:
        doi = entry.get("doi", "")
        if not doi:
            skipped_count += 1
            continue

        # Check if literature with this DOI already exists
        existing_lit = db.query(Literature).filter(Literature.doi == doi, Literature.project_id == project_id).first()

        if existing_lit:
            # Update the existing record
            existing_lit.ris_type = entry.get("type", existing_lit.ris_type)
            existing_lit.authors = ", ".join(entry.get("authors", [])) or existing_lit.authors
            existing_lit.title = entry.get("title", existing_lit.title)

            # Extract journal and publisher names using helper functions
            journal = extract_journal_from_ris_entry(entry)
            publisher = extract_publisher_from_ris_entry(entry)

            # Only update if we found a journal name
            if journal:
                existing_lit.journal = journal

            # Only update if we found a publisher name
            if publisher:
                existing_lit.publisher = publisher

            existing_lit.year = entry.get("year", existing_lit.year)
            existing_lit.abstract = entry.get("abstract", existing_lit.abstract)
            updated_count += 1
        else:
            skipped_count += 1

    db.commit()
    return {
        "message": "File processed successfully",
        "updated": updated_count,
        "skipped": skipped_count,
    }


@router.post("/full_texts")
async def get_literature_full_texts(
    lit_filter: LiteratureFilter,
    db: Session = Depends(get_db),
) -> str:
    service = LiteratureService(db)
    return service.get_full_texts(
        lit_filter=lit_filter,
    )


class SummarizeRequest(BaseModel):
    prompt: str
    content: str
    llm_provider: LLMProviderType


@router.post("/summarize")
async def summarize_literature(
    request: SummarizeRequest,
    db: Session = Depends(get_db),
):
    llm = get_llm_provider(request.llm_provider, db)
    response = await llm.generate_response_text(f"{request.prompt}\n\nContent:\n{request.content}")
    return {"summary": response}
    # return {"summary": f"{sum_req.prompt}\n\nContent:\n{sum_req.content}"}


@router.post("/export/ris")
async def export_ris(
    lit_filter: LiteratureFilter,
    db: Session = Depends(get_db),
) -> Response:
    service = LiteratureService(db)
    literature = service.get_literature_for_export(lit_filter)

    entries = []
    for lit in literature:
        entry = {
            "type": lit.ris_type or "JOUR",
            "doi": lit.doi,
            "authors": lit.authors.split(", ") if lit.authors else [],
            "title": lit.title,
            "journal_name": lit.journal,  # Use journal_name as primary field
            "secondary_title": lit.journal,  # Also include as secondary_title for compatibility
            "publisher": lit.publisher,  # PB tag in RIS
            "year": lit.year,
            "abstract": lit.abstract,
        }
        entries.append(entry)

    output = StringIO()
    rispy.dump(entries, output)

    return Response(
        content=output.getvalue(),
        media_type="application/x-research-info-systems",
        headers={"Content-Disposition": "attachment; filename=literature_export.ris"},
    )


@router.post("/export/bibtex")
async def export_bibtex(
    lit_filter: LiteratureFilter,
    db: Session = Depends(get_db),
) -> Response:
    service = LiteratureService(db)
    literature = service.get_literature_for_export(lit_filter)

    entries = {}

    for lit in literature:
        first_author_last_name = "unknown"
        if lit.authors and len(lit.authors) > 0:
            # Try to get the first author's last name
            authors_parts = lit.authors.split(",")
            if authors_parts:
                first_author_last_name = authors_parts[0].strip().split()[-1]

        # Get year
        year_str = str(lit.year) if lit.year else "0000"

        # Get first word of title
        first_word = "untitled"
        if lit.title:
            # Remove any non-alphanumeric characters and get the first word
            import re

            words = re.sub(r"[^\w\s]", "", lit.title).split()
            if words:
                first_word = words[0].lower()

        # Create the key
        key = f"{first_author_last_name}{year_str}{first_word}"

        # Create fields list
        fields = []
        if lit.title:
            fields.append(("title", lit.title))
        if lit.authors:
            fields.append(("author", lit.authors))
        if lit.journal:
            fields.append(("journal", lit.journal))
        if lit.publisher:
            fields.append(("publisher", lit.publisher))
        if lit.year:
            fields.append(("year", str(lit.year)))
        if lit.doi:
            fields.append(("doi", lit.doi))
        if lit.abstract:
            fields.append(("abstract", lit.abstract))

        # Create entry with fields
        entries[key] = Entry("article", fields)

    # Create bibliography data with all entries
    bib_data = BibliographyData(entries)

    # Write to string
    output = StringIO()
    BibtexWriter().write_stream(bib_data, output)

    return Response(
        content=output.getvalue(),
        media_type="application/x-bibtex",
        headers={"Content-Disposition": "attachment; filename=literature_export.bib"},
    )


class ReviewerCategoryColumn(BaseModel):
    reviewer_id: int
    category: str


class CSVExportRequest(BaseModel):
    lit_filter: LiteratureFilter
    columns: List[str]
    reviewer_ids: List[int] = []
    reviewer_category_columns: List[ReviewerCategoryColumn] = []


@router.post("/export/csv")
async def export_csv(
    request: CSVExportRequest,
    db: Session = Depends(get_db),
) -> Response:
    service = LiteratureService(db)
    literature = service.get_literature_for_export(request.lit_filter)

    output = StringIO()
    writer = csv.writer(output)

    # Write header row
    header = []
    for column in request.columns:
        if column == "id":
            header.append("ID")
        elif column == "doi":
            header.append("DOI")
        elif column == "full_text_available":
            header.append("Full Text Available")
        elif column.startswith("reviewer_"):
            reviewer_id = int(column.split("_")[1])
            reviewer = db.query(Reviewer).filter(Reviewer.id == reviewer_id).first()
            if reviewer:
                header.append(f"Reviewer: {reviewer.name} (Decision)")
                header.append(f"Reviewer: {reviewer.name} (Reason)")
        else:
            # Add other basic columns
            header.append(column.capitalize())

    # Add reviewer-category columns to header
    for reviewer_category in request.reviewer_category_columns:
        reviewer = db.query(Reviewer).filter(Reviewer.id == reviewer_category.reviewer_id).first()
        if reviewer:
            header.append(f"{reviewer.name}: {reviewer_category.category}")

    writer.writerow(header)

    # Write data rows
    for lit in literature:
        row = []
        for column in request.columns:
            if column == "id":
                row.append(lit.id)
            elif column == "doi":
                row.append(lit.doi)
            elif column == "full_text_available":
                row.append("Yes" if lit.full_text_available else "No")
            elif column == "title":
                row.append(lit.title)
            elif column == "authors":
                row.append(lit.authors)
            elif column == "journal":
                row.append(lit.journal)
            elif column == "publisher":
                row.append(lit.publisher)
            elif column == "year":
                row.append(lit.year)
            elif column == "abstract":
                row.append(lit.abstract)
            elif column.startswith("reviewer_"):
                reviewer_id = int(column.split("_")[1])
                decision = next(
                    (d for d in lit.review_decisions if d.reviewer_id == reviewer_id),
                    None,
                )
                if decision:
                    row.append(decision.decision)
                    row.append(decision.reason)
                else:
                    row.append("N/A")
                    row.append("N/A")

        # Add reviewer-category values to row
        for reviewer_category in request.reviewer_category_columns:
            category_value = next(
                (
                    cat.value
                    for cat in lit.categories
                    if cat.reviewer_id == reviewer_category.reviewer_id and cat.category == reviewer_category.category
                ),
                "N/A",
            )
            row.append(category_value)

        writer.writerow(row)

    return Response(
        content=output.getvalue(),
        media_type="text/csv",
        headers={"Content-Disposition": "attachment; filename=literature_export.csv"},
    )


@router.post("/without-dois")
async def get_literature_without_dois(
    lit_filter: LiteratureFilter,
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    service = LiteratureService(db)
    return service.get_literature_without_dois(lit_filter)


@router.post("/find-doi-matches")
async def find_doi_matches(
    request: dict,
    db: Session = Depends(get_db),
) -> Dict[int, List[Dict[str, Any]]]:
    service = LiteratureService(db)
    return service.find_potential_doi_matches(project_id=request["project_id"], literature_ids=request["literature_ids"])


@router.post("/find-duplicates")
async def find_duplicates(
    request: dict,
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    service = LiteratureService(db)
    return service.find_duplicates(
        project_id=request["project_id"],
        limit=request.get("limit", 12000),  # Default to entire dataset
        offset=request.get("offset", 0),
        chunk_size=request.get("chunk_size", 1000),  # Default to 1000 papers per chunk
    )


class DOILookupRequest(BaseModel):
    title: str
    authors: Optional[str] = None


@router.post("/lookup-doi")
async def lookup_doi(
    request: DOILookupRequest,
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    service = LiteratureService(db)
    result = await service.lookup_doi_by_title(request.title, request.authors)
    return result


@router.delete("/{literature_id}")
async def delete_literature(
    literature_id: int,
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    """Delete a literature item by ID."""
    literature = db.query(Literature).filter(Literature.id == literature_id).first()

    if not literature:
        raise HTTPException(status_code=404, detail="Literature not found")

    # Delete the literature item
    db.delete(literature)
    db.commit()

    return {"message": f"Literature item with ID {literature_id} deleted successfully"}
