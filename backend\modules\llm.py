# llm.py
from abc import ABC, abstractmethod
import asyncio
from enum import Enum
import json
import anthropic
import ollama
import google.generativeai as genai
import re
from openai import AsyncOpenAI  # Add this import at the top
from sqlalchemy.orm import Session

from modules.db import <PERSON><PERSON><PERSON><PERSON>
from config import Settings


def get_api_key(key_name: str, db: Session = None):
    """Get API key from database or fallback to config"""
    if db:
        api_key = db.query(ApiKey).filter(ApiKey.name == key_name).first()
        if api_key and api_key.value:
            return api_key.value

    # Fallback to config
    if key_name == "CLAUDE_API_KEY":
        return Settings.CLAUDE_API_KEY
    elif key_name == "GEMINI_API_KEY":
        return Settings.GEMINI_API_KEY
    elif key_name == "OPENAI_API_KEY":
        return Settings.OPENAI_API_KEY
    elif key_name == "DEEPSEEK_API_KEY":
        return Settings.DEEPSEEK_API_KEY
    elif key_name == "CORE_API_KEY":
        return Settings.CORE_API_KEY
    return None


def get_prompt(research_topic, categories, content, is_full_text, title, abstract):
    add1 = "ACCEPTED or REJECTED" if is_full_text else "ACCEPTED, REJECTED, or NEEDS_FULL_TEXT"
    add2 = "" if is_full_text else "|NEEDS_FULL_TEXT"
    add_sanitycheck = "- valid_paper: does the paper abstract fit the content (yes/no)" if is_full_text else ""
    prompt = f"""You are a research assistant evaluating and categorizing academic papers.

    Based on the following research topic and paper content:
    1. Classify the paper as either {add1}
    2. Indicate the relevance of the content to the fulfilment of the research topic (1-10).
    3. Categorize the paper according to these user-defined categories:
    - Study Type (e.g. RCT, Meta-analysis, Observational)
    {categories}
    {add_sanitycheck}

    Research Topic: {research_topic}

    Title:
    {title}
    Abstract:
    {abstract}

    Paper Content:
    {content}

    Respond in JSON format only:
    {{
        "decision": "ACCEPTED|REJECTED|{add2}",
        "reason": "Brief explanation of decision",
        "categories": {{
            "study_type": "value",
            "relevance": 5,
            ... add aditional user defined categories
        }}
    }}
    """

    if len(prompt) > 250000:
        raise ValueError(f"Prompt too long: {title} - {len(prompt)} characters")

    return prompt


class LLMProviderType(str, Enum):
    CLAUDE_3_SONNET = "claude-3-5-sonnet-20241022"
    DEEPSEEK_14B = "deepseek-r1:14b"
    DEEPSEEK_1_5B = "deepseek-r1:1.5b"
    DEEPSEEK_REASONER = "deepseek-reasoner"
    GEMINI_FLASH_20 = "gemini-2.0-flash"
    GEMINI_FLASH_25 = "gemini-2.5-flash-preview-04-17"
    GEMINI_FLASH_255 = "gemini-2.5-flash-preview-05-20"
    OPENAI_O3_MINI = "o3-mini"


class LLMProvider(ABC):
    async def generate_response(self, prompt: str) -> dict:
        response = await self.generate_response_text(prompt)

        response = re.sub(r"<think>.*?</think>", "", response, flags=re.DOTALL).strip()

        if "```json\n" in response:
            response = response.split("```json\n")[1].split("```")[0].strip()
        try:
            ret = json.loads(response)
        except json.JSONDecodeError:
            ret = None
        return ret

    @abstractmethod
    async def generate_response_text(self, prompt: str) -> str:
        response = await asyncio.to_thread(self.model.generate_content, prompt)
        return response.text


class DeepSeekReasonerProvider(LLMProvider):
    def __init__(self, api_key: str):
        self.client = AsyncOpenAI(api_key=api_key, base_url="https://api.deepseek.com")
        self.model_id = "deepseek-reasoner"

    async def generate_response_text(self, prompt: str) -> str:
        response = await self.client.chat.completions.create(
            model=self.model_id,
            messages=[{"role": "user", "content": prompt}],
            max_tokens=4000,
        )
        return response.choices[0].message.content


class OpenAIProvider(LLMProvider):
    def __init__(self, api_key: str):
        self.client = AsyncOpenAI(api_key=api_key)
        self.model_id = "o3-mini"

    async def generate_response_text(self, prompt: str) -> str:
        response = await self.client.chat.completions.create(
            model=self.model_id,
            messages=[{"role": "user", "content": prompt}],
            temperature=0,
            max_tokens=1000,
        )
        return response.choices[0].message.content


class ClaudeProvider(LLMProvider):
    def __init__(self, api_key: str):
        self.client = anthropic.Client(api_key=api_key)
        self.model_id = "claude-3-5-sonnet-20241022"

    async def generate_response_text(self, prompt: str) -> dict:
        response = self.client.messages.create(
            model=self.model_id,
            max_tokens=1000,
            temperature=0,
            messages=[{"role": "user", "content": prompt}],
        )
        return response.content[0].text


class GeminiProvider(LLMProvider):
    def __init__(self, api_key: str, model_id: str):
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel(model_id)

    async def generate_response_text(self, prompt: str) -> dict:
        response = await asyncio.to_thread(self.model.generate_content, prompt)
        return response.text


class OllamaProvider(LLMProvider):
    def __init__(self, model_id: str):
        self.model_id = model_id

    async def generate_response_text(self, prompt: str) -> dict:
        response = ollama.chat(model=self.model_id, messages=[{"role": "user", "content": prompt}])

        return response["message"]["content"]


def get_llm_provider(provider_type: LLMProviderType, db: Session = None) -> LLMProvider:
    if provider_type == LLMProviderType.CLAUDE_3_SONNET:
        api_key = get_api_key("CLAUDE_API_KEY", db)
        return ClaudeProvider(api_key=api_key)
    elif provider_type.startswith("gemini"):
        api_key = get_api_key("GEMINI_API_KEY", db)
        return GeminiProvider(api_key=api_key, model_id=provider_type)
    elif provider_type in [LLMProviderType.DEEPSEEK_14B, LLMProviderType.DEEPSEEK_1_5B]:
        return OllamaProvider(model_id=provider_type)
    elif provider_type == LLMProviderType.OPENAI_O3_MINI:
        api_key = get_api_key("OPENAI_API_KEY", db)
        return OpenAIProvider(api_key=api_key)
    elif provider_type == LLMProviderType.DEEPSEEK_REASONER:
        api_key = get_api_key("DEEPSEEK_API_KEY", db)
        return DeepSeekReasonerProvider(api_key=api_key)
    else:
        raise ValueError(f"Unsupported LLM provider: {provider_type}")
