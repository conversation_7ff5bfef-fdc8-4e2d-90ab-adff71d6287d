import os
from typing import List


class Settings:
    # Auth setting

    SECRET_KEY = "YUtK9Wor"
    ALGORITHM = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES = 30

    # API settings
    CLAUDE_API_KEY: str = "************************************************************************************************************"
    GEMINI_API_KEY: str = "AIzaSyDXwlTKRecvWa5Rm1A5-gTFvZU8wSTAR3Q"
    OPENAI_API_KEY: str = "********************************************************************************************************************************************************************"
    CORE_API_KEY: str = "I7r9WouJlesnjm6Q4DAGdB52gwSLpv1b"
    DEEPSEEK_API_KEY: str = "***********************************"

    # CORS settings
    CORS_ORIGINS: List[str] = os.environ.get("CORS_ORIGINS", "*").split(",")
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: List[str] = ["*"]
    CORS_ALLOW_HEADERS: List[str] = ["*"]

    # # Rate limiting settings
    # MAX_REQUESTS: int = 2000
    # TIME_WINDOW: int = 60  # seconds

    # PDF settings
    PDF_DIRECTORY: str = "pdfs"

    # Unpaywall settings
    UNPAYWALL_EMAIL: str = "<EMAIL>"

    # Batch processing
    SIMULTANEOUS_LLM_REQUESTS: int = 10
    DEFAULT_PAGE_SIZE: int = 15

    # openAI o3-mini 5.000 / min ~ 80 / s

    class Config:
        env_file = ".env"
