import { API_BASE_URL } from '../config/api.ts';
import { Literature, LiteratureFilter, ReviewerCategoryColumn } from '../types';
import api from './api.ts';

interface LiteratureParams {
    page: number;
    perPage: number;
    lit_filter: LiteratureFilter;
}

// Add this interface
export interface LiteratureUpdateData {
    title?: string;
    authors?: string;
    abstract?: string;
    journal?: string;
    publisher?: string;
    year?: number;
    doi?: string;
    keywords?: string;
    user_keywords?: string;
    full_text_available?: boolean;
}

function _clean_filter(lit_filter: LiteratureFilter) {
    if (lit_filter.reviewer_decision_filters) {
        lit_filter.reviewer_decision_filters = lit_filter.reviewer_decision_filters.filter((f) => {
            return f.decision != null;
        });
    }

    if (lit_filter.category_filters) {
        lit_filter.category_filters = lit_filter.category_filters.filter((f) => {
            return f.category != null;
        });
    }
}

export const literatureService = {
    // Add this method to the literatureService object
    async updateLiterature(
        id: number,
        updateData: LiteratureUpdateData
    ): Promise<{ message: string; item: Literature }> {
        const response = await api.put(`/literature/${id}`, updateData);
        return response.data;
    },

    async uploadRisFile(formData: FormData, onlyNew: boolean = false) {
        // Add the only_new parameter if true
        if (onlyNew) {
            formData.append('only_new', 'true');
        }

        const response = await api.post('/literature/upload-ris', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
        return response;
    },

    async uploadRisFileOnlyUpdate(formData: FormData) {
        const response = await api.post('/literature/upload-ris-update', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
        return response;
    },

    async getLiterature(params: LiteratureParams) {
        const { page, perPage, lit_filter } = params;
        _clean_filter(lit_filter);
        const response = await api.post(`/literature/list?page=${page}&per_page=${perPage}`, lit_filter);
        return response.data;
    },

    async getFullTexts(lit_filter: LiteratureFilter) {
        const response = await api.post('/literature/full_texts', lit_filter);
        return response.data;
    },

    async getCategories() {
        const response = await api.get('/categories');
        return response.data;
    },

    async getCategoryValues(category: string) {
        const response = await api.get(`/category-values/${category}`);
        return response.data;
    },

    async summarizeLiterature(prompt: string, content: string, llmProvider: string) {
        const response = await api.post('/literature/summarize', {
            prompt: prompt,
            content: content,
            llm_provider: llmProvider,
        });
        return response.data;
    },

    async exportAsRIS(lit_filter: any): Promise<Blob> {
        const response = await api.post('/literature/export/ris', lit_filter, {
            responseType: 'blob',
        });
        return response.data;
    },
    async exportAsCSV(
        filter: any,
        columns: string[],
        reviewerIds: number[] = [],
        reviewerCategoryColumns: ReviewerCategoryColumn[] = []
    ): Promise<Blob> {
        try {
            const response = await api.post(
                '/literature/export/csv',
                {
                    lit_filter: filter,
                    columns: columns,
                    reviewer_ids: reviewerIds,
                    reviewer_category_columns: reviewerCategoryColumns,
                },
                {
                    responseType: 'blob',
                }
            );
            return response.data;
        } catch (error) {
            console.error('Error exporting CSV:', error);
            throw error;
        }
    },

    async exportAsBibtex(lit_filter: any): Promise<Blob> {
        const response = await api.post('/literature/export/bibtex', lit_filter, {
            responseType: 'blob',
        });
        return response.data;
    },

    getPdfUrl(doi: string) {
        const token = localStorage.getItem('token');
        return `${API_BASE_URL}/literature/pdf/${encodeURIComponent(doi)}?token=${token}`;
    },

    async getLiteratureWithoutDOIs(filter: any) {
        // Add the no_doi condition to the filter
        const requestFilter = {
            ...filter,
            paper_status: 'no_doi',
        };

        const response = await api.post('/literature/without-dois', requestFilter);
        return response.data;
    },

    async findPotentialDOIMatches(projectId: number, literatureIds: number[]) {
        const response = await api.post('/literature/find-doi-matches', {
            project_id: projectId,
            literature_ids: literatureIds,
        });
        return response.data;
    },

    async lookupDOIByTitle(title: string, authors?: string): Promise<any> {
        const response = await api.post('/literature/lookup-doi', {
            title,
            authors,
        });
        return response.data;
    },

    async findDuplicates(
        projectId: number,
        limit: number = 12000,
        offset: number = 0,
        chunkSize: number = 1000
    ): Promise<any> {
        const response = await api.post('/literature/find-duplicates', {
            project_id: projectId,
            limit,
            offset,
            chunk_size: chunkSize,
        });
        return response.data;
    },

    async deleteLiterature(id: number): Promise<{ message: string }> {
        const response = await api.delete(`/literature/${id}`);
        return response.data;
    },

    async retrieveSingleFullText(literatureId: number): Promise<any> {
        const response = await api.post(`/literature/retrieve-single-full-text/${literatureId}`);
        return response.data;
    },
};
